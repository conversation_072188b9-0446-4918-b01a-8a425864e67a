import { pgTable, text, serial, integer, boolean, timestamp, jsonb } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
});

export const projects = pgTable("projects", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  description: text("description"),
  userId: integer("user_id").notNull(),
  status: text("status").notNull().default("active"),
  createdAt: timestamp("created_at").defaultNow(),
});

export const agents = pgTable("agents", {
  id: serial("id").primaryKey(),
  projectId: integer("project_id").notNull(),
  name: text("name").notNull(),
  role: text("role").notNull(), // team_leader, product_manager, architect, engineer, data_analyst
  status: text("status").notNull().default("idle"), // idle, active, processing, waiting
  config: jsonb("config").default({}),
  lastActivity: timestamp("last_activity").defaultNow(),
});

export const workflowNodes = pgTable("workflow_nodes", {
  id: serial("id").primaryKey(),
  projectId: integer("project_id").notNull(),
  type: text("type").notNull(), // start, agent, end
  agentId: integer("agent_id"),
  position: jsonb("position").notNull(), // {x: number, y: number}
  config: jsonb("config").default({}),
});

export const workflowConnections = pgTable("workflow_connections", {
  id: serial("id").primaryKey(),
  projectId: integer("project_id").notNull(),
  sourceNodeId: integer("source_node_id").notNull(),
  targetNodeId: integer("target_node_id").notNull(),
});

export const messages = pgTable("messages", {
  id: serial("id").primaryKey(),
  projectId: integer("project_id").notNull(),
  agentId: integer("agent_id"),
  content: text("content").notNull(),
  type: text("type").notNull().default("message"), // message, system, error
  timestamp: timestamp("timestamp").defaultNow(),
});

export const tasks = pgTable("tasks", {
  id: serial("id").primaryKey(),
  projectId: integer("project_id").notNull(),
  agentId: integer("agent_id").notNull(),
  title: text("title").notNull(),
  description: text("description"),
  status: text("status").notNull().default("pending"), // pending, in_progress, completed, failed
  priority: text("priority").notNull().default("medium"), // low, medium, high, critical
  dependencies: text("dependencies").array().default([]),
  result: text("result"),
  estimatedHours: integer("estimated_hours"),
  actualHours: integer("actual_hours"),
  createdAt: timestamp("created_at").defaultNow(),
  completedAt: timestamp("completed_at"),
});

export const agentCollaborations = pgTable("agent_collaborations", {
  id: serial("id").primaryKey(),
  projectId: integer("project_id").notNull(),
  initiatorAgentId: integer("initiator_agent_id").notNull(),
  targetAgentId: integer("target_agent_id").notNull(),
  type: text("type").notNull(), // negotiation, resource_request, conflict_resolution, task_handoff
  subject: text("subject").notNull(),
  status: text("status").notNull().default("pending"), // pending, in_progress, resolved, rejected
  data: jsonb("data").default({}),
  createdAt: timestamp("created_at").defaultNow(),
  resolvedAt: timestamp("resolved_at"),
});

export const appDeployments = pgTable("app_deployments", {
  id: serial("id").primaryKey(),
  projectId: integer("project_id").notNull(),
  version: text("version").notNull(),
  status: text("status").notNull().default("pending"), // pending, building, deployed, failed
  deploymentUrl: text("deployment_url"),
  buildLogs: text("build_logs"),
  performanceMetrics: jsonb("performance_metrics").default({}),
  createdAt: timestamp("created_at").defaultNow(),
  deployedAt: timestamp("deployed_at"),
});

export const appAnalytics = pgTable("app_analytics", {
  id: serial("id").primaryKey(),
  projectId: integer("project_id").notNull(),
  deploymentId: integer("deployment_id").notNull(),
  metric: text("metric").notNull(), // page_views, api_calls, errors, performance
  value: integer("value").notNull(),
  timestamp: timestamp("timestamp").defaultNow(),
  metadata: jsonb("metadata").default({}),
});

export const learningData = pgTable("learning_data", {
  id: serial("id").primaryKey(),
  projectId: integer("project_id").notNull(),
  agentId: integer("agent_id").notNull(),
  category: text("category").notNull(), // success_pattern, failure_pattern, user_feedback, performance_data
  data: jsonb("data").notNull(),
  score: integer("score"), // success/failure rating 1-10
  createdAt: timestamp("created_at").defaultNow(),
});

export const codeGenerations = pgTable("code_generations", {
  id: serial("id").primaryKey(),
  projectId: integer("project_id").notNull(),
  agentId: integer("agent_id").notNull(),
  type: text("type").notNull(), // frontend, backend, database, component, asset
  language: text("language"), // javascript, typescript, sql, css, etc
  prompt: text("prompt").notNull(),
  generatedCode: text("generated_code").notNull(),
  quality: integer("quality"), // AI-assessed quality score 1-10
  status: text("status").notNull().default("generated"), // generated, reviewed, integrated, rejected
  createdAt: timestamp("created_at").defaultNow(),
});

// Insert schemas
export const insertUserSchema = createInsertSchema(users).pick({
  username: true,
  password: true,
});

export const insertProjectSchema = createInsertSchema(projects).pick({
  name: true,
  description: true,
  userId: true,
});

export const insertAgentSchema = createInsertSchema(agents).pick({
  projectId: true,
  name: true,
  role: true,
  config: true,
});

export const insertWorkflowNodeSchema = createInsertSchema(workflowNodes).pick({
  projectId: true,
  type: true,
  agentId: true,
  position: true,
  config: true,
});

export const insertWorkflowConnectionSchema = createInsertSchema(workflowConnections).pick({
  projectId: true,
  sourceNodeId: true,
  targetNodeId: true,
});

export const insertMessageSchema = createInsertSchema(messages).pick({
  projectId: true,
  agentId: true,
  content: true,
  type: true,
});

export const insertTaskSchema = createInsertSchema(tasks).pick({
  projectId: true,
  agentId: true,
  title: true,
  description: true,
  priority: true,
  dependencies: true,
  estimatedHours: true,
});

export const insertAgentCollaborationSchema = createInsertSchema(agentCollaborations).pick({
  projectId: true,
  initiatorAgentId: true,
  targetAgentId: true,
  type: true,
  subject: true,
  data: true,
});

export const insertAppDeploymentSchema = createInsertSchema(appDeployments).pick({
  projectId: true,
  version: true,
  status: true,
  deploymentUrl: true,
  buildLogs: true,
  performanceMetrics: true,
});

export const insertAppAnalyticsSchema = createInsertSchema(appAnalytics).pick({
  projectId: true,
  deploymentId: true,
  metric: true,
  value: true,
  metadata: true,
});

export const insertLearningDataSchema = createInsertSchema(learningData).pick({
  projectId: true,
  agentId: true,
  category: true,
  data: true,
  score: true,
});

export const insertCodeGenerationSchema = createInsertSchema(codeGenerations).pick({
  projectId: true,
  agentId: true,
  type: true,
  language: true,
  prompt: true,
  generatedCode: true,
  quality: true,
  status: true,
});

// Types
export type User = typeof users.$inferSelect;
export type InsertUser = z.infer<typeof insertUserSchema>;
export type Project = typeof projects.$inferSelect;
export type InsertProject = z.infer<typeof insertProjectSchema>;
export type Agent = typeof agents.$inferSelect;
export type InsertAgent = z.infer<typeof insertAgentSchema>;
export type WorkflowNode = typeof workflowNodes.$inferSelect;
export type InsertWorkflowNode = z.infer<typeof insertWorkflowNodeSchema>;
export type WorkflowConnection = typeof workflowConnections.$inferSelect;
export type InsertWorkflowConnection = z.infer<typeof insertWorkflowConnectionSchema>;
export type Message = typeof messages.$inferSelect;
export type InsertMessage = z.infer<typeof insertMessageSchema>;
export type Task = typeof tasks.$inferSelect;
export type InsertTask = z.infer<typeof insertTaskSchema>;
export type AgentCollaboration = typeof agentCollaborations.$inferSelect;
export type InsertAgentCollaboration = z.infer<typeof insertAgentCollaborationSchema>;
export type AppDeployment = typeof appDeployments.$inferSelect;
export type InsertAppDeployment = z.infer<typeof insertAppDeploymentSchema>;
export type AppAnalytics = typeof appAnalytics.$inferSelect;
export type InsertAppAnalytics = z.infer<typeof insertAppAnalyticsSchema>;
export type LearningData = typeof learningData.$inferSelect;
export type InsertLearningData = z.infer<typeof insertLearningDataSchema>;
export type CodeGeneration = typeof codeGenerations.$inferSelect;
export type InsertCodeGeneration = z.infer<typeof insertCodeGenerationSchema>;
