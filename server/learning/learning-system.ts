interface LearningRecord {
  id: string;
  projectId: number;
  agentId: number | null;
  category: "success_pattern" | "failure_pattern" | "user_feedback" | "performance_data";
  data: any;
  score: number; // 1-10 rating
  timestamp: Date;
  context: string;
}

interface AgentPerformance {
  agentId: number;
  totalInteractions: number;
  successRate: number;
  averageResponseTime: number;
  userSatisfactionScore: number;
  improvementTrend: number;
  strengths: string[];
  weaknesses: string[];
}

interface LearningInsight {
  type: "pattern" | "recommendation" | "optimization";
  description: string;
  confidence: number;
  impact: "low" | "medium" | "high";
  actionable: boolean;
  data: any;
}

interface AdaptationStrategy {
  agentId: number;
  strategy: string;
  parameters: Record<string, any>;
  expectedImprovement: number;
  implementationPlan: string[];
}

export class LearningSystem {
  private learningRecords: Map<string, LearningRecord> = new Map();
  private agentPerformance: Map<number, AgentPerformance> = new Map();
  private patterns: Map<string, any> = new Map();
  private adaptationStrategies: Map<number, AdaptationStrategy[]> = new Map();

  async recordInteraction(
    projectId: number,
    agentId: number | null,
    userInput: string,
    agentResponse: string,
    score?: number
  ): Promise<void> {
    const recordId = `interaction_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const record: LearningRecord = {
      id: recordId,
      projectId,
      agentId,
      category: "performance_data",
      data: {
        userInput,
        agentResponse,
        responseLength: agentResponse.length,
        inputComplexity: this.calculateComplexity(userInput),
        responseQuality: score || this.estimateResponseQuality(userInput, agentResponse)
      },
      score: score || this.estimateResponseQuality(userInput, agentResponse),
      timestamp: new Date(),
      context: `Project ${projectId} interaction`
    };

    this.learningRecords.set(recordId, record);
    
    if (agentId) {
      await this.updateAgentPerformance(agentId, record);
    }

    // Analyze for patterns
    await this.analyzePatterns(record);
    
    // Trigger adaptation if needed
    await this.triggerAdaptation(agentId, record);
  }

  async recordSuccess(
    projectId: number,
    agentId: number,
    successData: any,
    context: string
  ): Promise<void> {
    const recordId = `success_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const record: LearningRecord = {
      id: recordId,
      projectId,
      agentId,
      category: "success_pattern",
      data: successData,
      score: 8, // High score for success
      timestamp: new Date(),
      context
    };

    this.learningRecords.set(recordId, record);
    await this.updateAgentPerformance(agentId, record);
    await this.analyzePatterns(record);
  }

  async recordFailure(
    projectId: number,
    agentId: number,
    failureData: any,
    context: string
  ): Promise<void> {
    const recordId = `failure_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const record: LearningRecord = {
      id: recordId,
      projectId,
      agentId,
      category: "failure_pattern",
      data: failureData,
      score: 2, // Low score for failure
      timestamp: new Date(),
      context
    };

    this.learningRecords.set(recordId, record);
    await this.updateAgentPerformance(agentId, record);
    await this.analyzePatterns(record);
    
    // Generate immediate improvement suggestions
    await this.generateImprovementSuggestions(agentId, record);
  }

  async recordUserFeedback(
    projectId: number,
    agentId: number | null,
    feedback: string,
    rating: number,
    context: string
  ): Promise<void> {
    const recordId = `feedback_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const record: LearningRecord = {
      id: recordId,
      projectId,
      agentId,
      category: "user_feedback",
      data: {
        feedback,
        rating,
        sentiment: this.analyzeSentiment(feedback)
      },
      score: rating,
      timestamp: new Date(),
      context
    };

    this.learningRecords.set(recordId, record);
    
    if (agentId) {
      await this.updateAgentPerformance(agentId, record);
    }
  }

  async getAgentInsights(agentId: number): Promise<{
    performance: AgentPerformance;
    learnings: LearningInsight[];
    suggestions: string[];
  }> {
    const performance = this.agentPerformance.get(agentId);
    if (!performance) {
      return {
        performance: this.createDefaultPerformance(agentId),
        learnings: [],
        suggestions: ["No performance data available yet"]
      };
    }

    const learnings = await this.generateLearningInsights(agentId);
    const suggestions = await this.generateImprovementSuggestions(agentId);

    return {
      performance,
      learnings,
      suggestions
    };
  }

  async getProjectLearnings(projectId: number): Promise<{
    patterns: any[];
    insights: LearningInsight[];
    recommendations: string[];
  }> {
    const projectRecords = Array.from(this.learningRecords.values())
      .filter(record => record.projectId === projectId);

    const patterns = await this.extractProjectPatterns(projectRecords);
    const insights = await this.generateProjectInsights(projectRecords);
    const recommendations = await this.generateProjectRecommendations(patterns, insights);

    return {
      patterns,
      insights,
      recommendations
    };
  }

  async adaptAgent(agentId: number, adaptationData: any): Promise<AdaptationStrategy> {
    const performance = this.agentPerformance.get(agentId);
    if (!performance) {
      throw new Error("No performance data available for agent");
    }

    // Analyze current weaknesses
    const weaknesses = performance.weaknesses;
    const strategy = await this.createAdaptationStrategy(agentId, weaknesses, adaptationData);

    // Store strategy
    if (!this.adaptationStrategies.has(agentId)) {
      this.adaptationStrategies.set(agentId, []);
    }
    this.adaptationStrategies.get(agentId)!.push(strategy);

    return strategy;
  }

  async evolveAgent(agentId: number): Promise<{
    currentCapabilities: string[];
    newCapabilities: string[];
    evolutionPlan: string[];
  }> {
    const performance = this.agentPerformance.get(agentId);
    if (!performance) {
      throw new Error("No performance data available for agent");
    }

    const currentCapabilities = performance.strengths;
    const learningRecords = Array.from(this.learningRecords.values())
      .filter(record => record.agentId === agentId);

    const newCapabilities = await this.identifyNewCapabilities(learningRecords);
    const evolutionPlan = await this.createEvolutionPlan(agentId, currentCapabilities, newCapabilities);

    return {
      currentCapabilities,
      newCapabilities,
      evolutionPlan
    };
  }

  private async updateAgentPerformance(agentId: number, record: LearningRecord): Promise<void> {
    let performance = this.agentPerformance.get(agentId);
    
    if (!performance) {
      performance = this.createDefaultPerformance(agentId);
      this.agentPerformance.set(agentId, performance);
    }

    // Update metrics
    performance.totalInteractions++;
    
    // Update success rate
    const recentRecords = Array.from(this.learningRecords.values())
      .filter(r => r.agentId === agentId)
      .slice(-100); // Last 100 interactions
    
    const successfulInteractions = recentRecords.filter(r => r.score >= 6).length;
    performance.successRate = successfulInteractions / recentRecords.length;

    // Update user satisfaction
    const feedbackRecords = recentRecords.filter(r => r.category === "user_feedback");
    if (feedbackRecords.length > 0) {
      performance.userSatisfactionScore = feedbackRecords.reduce((sum, r) => sum + r.score, 0) / feedbackRecords.length;
    }

    // Update improvement trend
    const oldRecords = recentRecords.slice(0, 50);
    const newRecords = recentRecords.slice(-50);
    
    if (oldRecords.length > 0 && newRecords.length > 0) {
      const oldAvg = oldRecords.reduce((sum, r) => sum + r.score, 0) / oldRecords.length;
      const newAvg = newRecords.reduce((sum, r) => sum + r.score, 0) / newRecords.length;
      performance.improvementTrend = newAvg - oldAvg;
    }

    // Update strengths and weaknesses
    await this.updateStrengthsAndWeaknesses(performance, recentRecords);
  }

  private async analyzePatterns(record: LearningRecord): Promise<void> {
    const patternKey = `${record.category}_${record.agentId || 'global'}`;
    
    if (!this.patterns.has(patternKey)) {
      this.patterns.set(patternKey, {
        category: record.category,
        agentId: record.agentId,
        occurrences: 0,
        averageScore: 0,
        commonElements: [],
        trends: []
      });
    }

    const pattern = this.patterns.get(patternKey)!;
    pattern.occurrences++;
    pattern.averageScore = (pattern.averageScore * (pattern.occurrences - 1) + record.score) / pattern.occurrences;

    // Analyze common elements
    if (record.data && typeof record.data === 'object') {
      this.updateCommonElements(pattern, record.data);
    }
  }

  private async triggerAdaptation(agentId: number | null, record: LearningRecord): Promise<void> {
    if (!agentId || record.score >= 6) return; // Only adapt on poor performance

    const performance = this.agentPerformance.get(agentId);
    if (!performance) return;

    // Check if adaptation is needed
    if (performance.successRate < 0.7 || performance.improvementTrend < -0.5) {
      await this.adaptAgent(agentId, {
        trigger: "poor_performance",
        record: record,
        performance: performance
      });
    }
  }

  private calculateComplexity(text: string): number {
    // Simple complexity calculation based on length, vocabulary, and structure
    const words = text.split(/\s+/).length;
    const uniqueWords = new Set(text.toLowerCase().split(/\s+/)).size;
    const sentences = text.split(/[.!?]+/).length;
    
    return (words * 0.3 + uniqueWords * 0.5 + sentences * 0.2) / 10;
  }

  private estimateResponseQuality(input: string, response: string): number {
    // Simple quality estimation
    const relevanceScore = this.calculateRelevance(input, response);
    const completenessScore = this.calculateCompleteness(response);
    const clarityScore = this.calculateClarity(response);
    
    return Math.round((relevanceScore + completenessScore + clarityScore) / 3);
  }

  private calculateRelevance(input: string, response: string): number {
    // Simple keyword matching
    const inputWords = new Set(input.toLowerCase().split(/\s+/));
    const responseWords = new Set(response.toLowerCase().split(/\s+/));
    const intersection = new Set([...inputWords].filter(x => responseWords.has(x)));
    
    return Math.min(10, (intersection.size / inputWords.size) * 10);
  }

  private calculateCompleteness(response: string): number {
    // Based on response length and structure
    const words = response.split(/\s+/).length;
    const sentences = response.split(/[.!?]+/).length;
    
    if (words < 10) return 3;
    if (words < 50) return 6;
    if (words < 100) return 8;
    return 10;
  }

  private calculateClarity(response: string): number {
    // Simple clarity metrics
    const words = response.split(/\s+/).length;
    const sentences = response.split(/[.!?]+/).length;
    const avgWordsPerSentence = words / sentences;
    
    if (avgWordsPerSentence > 30) return 5; // Too complex
    if (avgWordsPerSentence < 5) return 6; // Too simple
    return 8; // Good balance
  }

  private analyzeSentiment(text: string): "positive" | "negative" | "neutral" {
    // Simple sentiment analysis
    const positiveWords = ["good", "great", "excellent", "amazing", "helpful", "useful"];
    const negativeWords = ["bad", "terrible", "awful", "useless", "unhelpful", "poor"];
    
    const words = text.toLowerCase().split(/\s+/);
    const positiveCount = words.filter(word => positiveWords.includes(word)).length;
    const negativeCount = words.filter(word => negativeWords.includes(word)).length;
    
    if (positiveCount > negativeCount) return "positive";
    if (negativeCount > positiveCount) return "negative";
    return "neutral";
  }

  private createDefaultPerformance(agentId: number): AgentPerformance {
    return {
      agentId,
      totalInteractions: 0,
      successRate: 0.5,
      averageResponseTime: 1000,
      userSatisfactionScore: 5,
      improvementTrend: 0,
      strengths: [],
      weaknesses: []
    };
  }

  private async generateLearningInsights(agentId: number): Promise<LearningInsight[]> {
    const insights: LearningInsight[] = [];
    const records = Array.from(this.learningRecords.values())
      .filter(record => record.agentId === agentId);

    // Pattern-based insights
    const successPatterns = records.filter(r => r.category === "success_pattern");
    if (successPatterns.length > 0) {
      insights.push({
        type: "pattern",
        description: `Agent performs well in ${successPatterns[0].context} scenarios`,
        confidence: 0.8,
        impact: "medium",
        actionable: true,
        data: successPatterns
      });
    }

    // Performance insights
    const performance = this.agentPerformance.get(agentId);
    if (performance && performance.improvementTrend > 0.5) {
      insights.push({
        type: "optimization",
        description: "Agent is showing consistent improvement over time",
        confidence: 0.9,
        impact: "high",
        actionable: false,
        data: { trend: performance.improvementTrend }
      });
    }

    return insights;
  }

  private async generateImprovementSuggestions(agentId: number, failureRecord?: LearningRecord): Promise<string[]> {
    const suggestions: string[] = [];
    const performance = this.agentPerformance.get(agentId);
    
    if (!performance) return ["Collect more performance data"];

    if (performance.successRate < 0.7) {
      suggestions.push("Focus on improving response accuracy and relevance");
    }

    if (performance.userSatisfactionScore < 6) {
      suggestions.push("Work on making responses more helpful and user-friendly");
    }

    if (performance.improvementTrend < -0.2) {
      suggestions.push("Review recent changes and revert problematic updates");
    }

    if (failureRecord) {
      suggestions.push(`Address specific issue: ${failureRecord.context}`);
    }

    return suggestions;
  }

  private async updateStrengthsAndWeaknesses(performance: AgentPerformance, records: LearningRecord[]): Promise<void> {
    const strengths: string[] = [];
    const weaknesses: string[] = [];

    // Analyze by category
    const categories = ["success_pattern", "failure_pattern", "user_feedback", "performance_data"];
    
    for (const category of categories) {
      const categoryRecords = records.filter(r => r.category === category);
      if (categoryRecords.length === 0) continue;

      const avgScore = categoryRecords.reduce((sum, r) => sum + r.score, 0) / categoryRecords.length;
      
      if (avgScore >= 7) {
        strengths.push(`Strong performance in ${category.replace('_', ' ')}`);
      } else if (avgScore <= 4) {
        weaknesses.push(`Needs improvement in ${category.replace('_', ' ')}`);
      }
    }

    performance.strengths = strengths;
    performance.weaknesses = weaknesses;
  }

  private updateCommonElements(pattern: any, data: any): void {
    // Simple implementation - in production, use more sophisticated pattern recognition
    if (typeof data === 'object') {
      for (const [key, value] of Object.entries(data)) {
        if (typeof value === 'string' || typeof value === 'number') {
          const element = `${key}:${value}`;
          if (!pattern.commonElements.includes(element)) {
            pattern.commonElements.push(element);
          }
        }
      }
    }
  }

  private async extractProjectPatterns(records: LearningRecord[]): Promise<any[]> {
    const patterns = [];
    const categories = [...new Set(records.map(r => r.category))];
    
    for (const category of categories) {
      const categoryRecords = records.filter(r => r.category === category);
      const avgScore = categoryRecords.reduce((sum, r) => sum + r.score, 0) / categoryRecords.length;
      
      patterns.push({
        category,
        occurrences: categoryRecords.length,
        averageScore: avgScore,
        trend: this.calculateTrend(categoryRecords)
      });
    }

    return patterns;
  }

  private calculateTrend(records: LearningRecord[]): number {
    if (records.length < 2) return 0;
    
    const sortedRecords = records.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
    const firstHalf = sortedRecords.slice(0, Math.floor(sortedRecords.length / 2));
    const secondHalf = sortedRecords.slice(Math.floor(sortedRecords.length / 2));
    
    const firstAvg = firstHalf.reduce((sum, r) => sum + r.score, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((sum, r) => sum + r.score, 0) / secondHalf.length;
    
    return secondAvg - firstAvg;
  }

  private async generateProjectInsights(records: LearningRecord[]): Promise<LearningInsight[]> {
    const insights: LearningInsight[] = [];
    
    const successRate = records.filter(r => r.score >= 6).length / records.length;
    if (successRate > 0.8) {
      insights.push({
        type: "pattern",
        description: "Project shows high success rate across all interactions",
        confidence: 0.9,
        impact: "high",
        actionable: false,
        data: { successRate }
      });
    }

    return insights;
  }

  private async generateProjectRecommendations(patterns: any[], insights: LearningInsight[]): Promise<string[]> {
    const recommendations: string[] = [];
    
    for (const pattern of patterns) {
      if (pattern.averageScore < 5) {
        recommendations.push(`Improve ${pattern.category.replace('_', ' ')} handling`);
      }
    }

    return recommendations;
  }

  private async createAdaptationStrategy(agentId: number, weaknesses: string[], adaptationData: any): Promise<AdaptationStrategy> {
    return {
      agentId,
      strategy: "Focused improvement on identified weaknesses",
      parameters: {
        weaknesses,
        adaptationData,
        targetImprovement: 0.2
      },
      expectedImprovement: 0.15,
      implementationPlan: [
        "Analyze failure patterns",
        "Adjust response generation parameters",
        "Implement additional validation",
        "Monitor improvement metrics"
      ]
    };
  }

  private async identifyNewCapabilities(records: LearningRecord[]): Promise<string[]> {
    const capabilities: string[] = [];
    
    // Analyze successful interactions for new patterns
    const successfulRecords = records.filter(r => r.score >= 8);
    
    if (successfulRecords.length > 10) {
      capabilities.push("Advanced problem solving");
    }

    if (records.some(r => r.data?.responseLength > 500)) {
      capabilities.push("Detailed explanations");
    }

    return capabilities;
  }

  private async createEvolutionPlan(agentId: number, current: string[], newCapabilities: string[]): Promise<string[]> {
    const plan: string[] = [];
    
    plan.push("Assess current performance baseline");
    plan.push("Identify capability gaps");
    
    for (const capability of newCapabilities) {
      plan.push(`Develop ${capability} capability`);
      plan.push(`Test ${capability} in controlled environment`);
    }
    
    plan.push("Gradual rollout of new capabilities");
    plan.push("Monitor performance impact");
    plan.push("Adjust based on feedback");

    return plan;
  }
}
