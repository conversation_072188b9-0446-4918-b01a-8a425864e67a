import { exec } from "child_process";
import { promisify } from "util";
import fs from "fs/promises";
import path from "path";

const execAsync = promisify(exec);

interface DeploymentConfig {
  projectId: number;
  version: string;
  environment: "development" | "staging" | "production";
  buildCommand?: string;
  deployCommand?: string;
  healthCheckUrl?: string;
}

interface DeploymentResult {
  status: "pending" | "building" | "deployed" | "failed";
  url?: string;
  logs: string;
  buildTime?: number;
  deployTime?: number;
}

interface ContainerConfig {
  image: string;
  port: number;
  environment: Record<string, string>;
  volumes?: string[];
  networks?: string[];
}

export class DeploymentManager {
  private deployments: Map<string, DeploymentResult> = new Map();
  private buildQueue: Array<{ id: string; config: DeploymentConfig }> = [];
  private isProcessing = false;

  async deployApp(projectId: number, version: string): Promise<DeploymentResult> {
    const deploymentId = `deploy_${projectId}_${version}_${Date.now()}`;
    
    const config: DeploymentConfig = {
      projectId,
      version,
      environment: "production",
      buildCommand: "npm run build",
      deployCommand: "docker-compose up -d",
      healthCheckUrl: `https://app-${projectId}.buildmaster.dev`
    };

    const result: DeploymentResult = {
      status: "pending",
      logs: "Deployment queued...\n"
    };

    this.deployments.set(deploymentId, result);
    this.buildQueue.push({ id: deploymentId, config });

    // Start processing if not already running
    if (!this.isProcessing) {
      this.processQueue();
    }

    return result;
  }

  async getDeploymentStatus(deploymentId: string): Promise<DeploymentResult | null> {
    return this.deployments.get(deploymentId) || null;
  }

  async rollbackDeployment(deploymentId: string, targetVersion: string): Promise<DeploymentResult> {
    const deployment = this.deployments.get(deploymentId);
    if (!deployment) {
      throw new Error("Deployment not found");
    }

    const rollbackResult: DeploymentResult = {
      status: "pending",
      logs: `Rolling back to version ${targetVersion}...\n`
    };

    // Implement rollback logic
    try {
      rollbackResult.logs += await this.executeRollback(deploymentId, targetVersion);
      rollbackResult.status = "deployed";
    } catch (error) {
      rollbackResult.status = "failed";
      rollbackResult.logs += `Rollback failed: ${error.message}\n`;
    }

    return rollbackResult;
  }

  async scaleDeployment(deploymentId: string, replicas: number): Promise<boolean> {
    try {
      const scaleCommand = `docker-compose scale app=${replicas}`;
      await execAsync(scaleCommand);
      return true;
    } catch (error) {
      console.error("Scaling failed:", error);
      return false;
    }
  }

  async getDeploymentLogs(deploymentId: string, lines: number = 100): Promise<string> {
    try {
      const { stdout } = await execAsync(`docker-compose logs --tail=${lines} app`);
      return stdout;
    } catch (error) {
      return `Failed to retrieve logs: ${error.message}`;
    }
  }

  async getDeploymentMetrics(deploymentId: string): Promise<any> {
    try {
      // Get container stats
      const { stdout } = await execAsync("docker stats --no-stream --format 'table {{.Container}}\\t{{.CPUPerc}}\\t{{.MemUsage}}\\t{{.NetIO}}\\t{{.BlockIO}}'");
      
      return {
        timestamp: new Date().toISOString(),
        containers: this.parseDockerStats(stdout),
        uptime: await this.getUptime(deploymentId),
        healthStatus: await this.checkHealth(deploymentId)
      };
    } catch (error) {
      return {
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  private async processQueue(): Promise<void> {
    this.isProcessing = true;

    while (this.buildQueue.length > 0) {
      const { id, config } = this.buildQueue.shift()!;
      await this.processBuild(id, config);
    }

    this.isProcessing = false;
  }

  private async processBuild(deploymentId: string, config: DeploymentConfig): Promise<void> {
    const result = this.deployments.get(deploymentId)!;
    const startTime = Date.now();

    try {
      result.status = "building";
      result.logs += "Starting build process...\n";

      // Generate project files
      await this.generateProjectFiles(config);
      result.logs += "Project files generated...\n";

      // Build the application
      if (config.buildCommand) {
        result.logs += `Executing build command: ${config.buildCommand}\n`;
        const buildOutput = await this.executeBuildCommand(config);
        result.logs += buildOutput;
      }

      result.buildTime = Date.now() - startTime;
      result.logs += `Build completed in ${result.buildTime}ms\n`;

      // Deploy the application
      result.status = "building";
      result.logs += "Starting deployment...\n";
      
      const deployOutput = await this.executeDeployment(config);
      result.logs += deployOutput;

      result.deployTime = Date.now() - startTime - result.buildTime;
      result.url = config.healthCheckUrl;
      result.status = "deployed";
      result.logs += `Deployment completed in ${result.deployTime}ms\n`;

      // Health check
      if (config.healthCheckUrl) {
        const healthCheck = await this.performHealthCheck(config.healthCheckUrl);
        result.logs += `Health check: ${healthCheck ? "PASSED" : "FAILED"}\n`;
      }

    } catch (error) {
      result.status = "failed";
      result.logs += `Deployment failed: ${error.message}\n`;
      console.error(`Deployment ${deploymentId} failed:`, error);
    }
  }

  private async generateProjectFiles(config: DeploymentConfig): Promise<void> {
    const projectDir = path.join(process.cwd(), "deployments", `project_${config.projectId}`);
    
    // Create project directory
    await fs.mkdir(projectDir, { recursive: true });

    // Generate Dockerfile
    const dockerfile = this.generateDockerfile(config);
    await fs.writeFile(path.join(projectDir, "Dockerfile"), dockerfile);

    // Generate docker-compose.yml
    const dockerCompose = this.generateDockerCompose(config);
    await fs.writeFile(path.join(projectDir, "docker-compose.yml"), dockerCompose);

    // Generate package.json
    const packageJson = this.generatePackageJson(config);
    await fs.writeFile(path.join(projectDir, "package.json"), JSON.stringify(packageJson, null, 2));

    // Generate basic app files
    await this.generateAppFiles(projectDir, config);
  }

  private generateDockerfile(config: DeploymentConfig): string {
    return `
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .

EXPOSE 3000

CMD ["npm", "start"]
`;
  }

  private generateDockerCompose(config: DeploymentConfig): string {
    return `
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PROJECT_ID=${config.projectId}
      - VERSION=${config.version}
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - app
    restart: unless-stopped
`;
  }

  private generatePackageJson(config: DeploymentConfig): any {
    return {
      name: `buildmaster-app-${config.projectId}`,
      version: config.version,
      description: `Generated app for project ${config.projectId}`,
      main: "index.js",
      scripts: {
        start: "node index.js",
        build: "echo 'Build completed'",
        test: "echo 'Tests passed'"
      },
      dependencies: {
        express: "^4.18.0",
        cors: "^2.8.5"
      },
      engines: {
        node: ">=18.0.0"
      }
    };
  }

  private async generateAppFiles(projectDir: string, config: DeploymentConfig): Promise<void> {
    // Generate basic Express app
    const appCode = `
const express = require('express');
const cors = require('cors');

const app = express();
const port = process.env.PORT || 3000;

app.use(cors());
app.use(express.json());

app.get('/', (req, res) => {
  res.json({
    message: 'BuildMaster Generated App',
    projectId: ${config.projectId},
    version: '${config.version}',
    timestamp: new Date().toISOString()
  });
});

app.get('/health', (req, res) => {
  res.json({ status: 'healthy', uptime: process.uptime() });
});

app.listen(port, () => {
  console.log(\`App listening on port \${port}\`);
});
`;

    await fs.writeFile(path.join(projectDir, "index.js"), appCode);
  }

  private async executeBuildCommand(config: DeploymentConfig): Promise<string> {
    const projectDir = path.join(process.cwd(), "deployments", `project_${config.projectId}`);
    
    try {
      const { stdout, stderr } = await execAsync(config.buildCommand!, { cwd: projectDir });
      return stdout + stderr;
    } catch (error) {
      throw new Error(`Build failed: ${error.message}`);
    }
  }

  private async executeDeployment(config: DeploymentConfig): Promise<string> {
    const projectDir = path.join(process.cwd(), "deployments", `project_${config.projectId}`);
    
    try {
      const { stdout, stderr } = await execAsync(config.deployCommand!, { cwd: projectDir });
      return stdout + stderr;
    } catch (error) {
      throw new Error(`Deployment failed: ${error.message}`);
    }
  }

  private async performHealthCheck(url: string): Promise<boolean> {
    try {
      const response = await fetch(url);
      return response.ok;
    } catch {
      return false;
    }
  }

  private async executeRollback(deploymentId: string, targetVersion: string): Promise<string> {
    // Implement rollback logic
    return `Rollback to ${targetVersion} completed successfully.\n`;
  }

  private parseDockerStats(output: string): any[] {
    const lines = output.split('\n').slice(1); // Skip header
    return lines.filter(line => line.trim()).map(line => {
      const parts = line.split('\t');
      return {
        container: parts[0],
        cpu: parts[1],
        memory: parts[2],
        network: parts[3],
        block: parts[4]
      };
    });
  }

  private async getUptime(deploymentId: string): Promise<number> {
    try {
      const { stdout } = await execAsync("docker-compose ps --format json");
      const containers = JSON.parse(stdout);
      // Return uptime in seconds
      return Math.floor(Date.now() / 1000);
    } catch {
      return 0;
    }
  }

  private async checkHealth(deploymentId: string): Promise<string> {
    try {
      const response = await fetch("http://localhost:3000/health");
      return response.ok ? "healthy" : "unhealthy";
    } catch {
      return "unreachable";
    }
  }
}
