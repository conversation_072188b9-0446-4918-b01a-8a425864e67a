import type { Express } from "express";
import { createServer, type Server } from "http";
import { WebSocketServer, WebSocket } from "ws";
import { storage } from "./storage";
import {
  insertProjectSchema, insertAgentSchema, insertWorkflowNodeSchema,
  insertWorkflowConnectionSchema, insertMessageSchema, insertTaskSchema,
  insertAgentCollaborationSchema, insertAppDeploymentSchema,
  insertAppAnalyticsSchema, insertLearningDataSchema, insertCodeGenerationSchema
} from "@shared/schema";
import { SambaNovaAI } from "./ai/sambanova-ai";
import { CrewAIOrchestrator } from "./ai/crew-orchestrator";
import { LangChainProcessor } from "./ai/langchain-processor";
import { DeploymentManager } from "./deployment/deployment-manager";
import { AnalyticsEngine } from "./analytics/analytics-engine";
import { LearningSystem } from "./learning/learning-system";

export async function registerRoutes(app: Express): Promise<Server> {
  const httpServer = createServer(app);

  // Initialize AI services
  const sambaNovaAI = new SambaNovaAI();
  const crewOrchestrator = new CrewAIOrchestrator(sambaNovaAI);
  const langChainProcessor = new LangChainProcessor(sambaNovaAI);
  const deploymentManager = new DeploymentManager();
  const analyticsEngine = new AnalyticsEngine();
  const learningSystem = new LearningSystem();

  // WebSocket server for real-time updates
  const wss = new WebSocketServer({ server: httpServer, path: '/ws' });

  const clients = new Set<WebSocket>();

  wss.on('connection', (ws) => {
    clients.add(ws);

    ws.on('close', () => {
      clients.delete(ws);
    });

    ws.on('message', async (data) => {
      try {
        const message = JSON.parse(data.toString());

        if (message.type === 'join_project') {
          ws.send(JSON.stringify({ type: 'joined', projectId: message.projectId }));
        }
      } catch (error) {
        console.error('WebSocket message error:', error);
      }
    });
  });

  function broadcast(data: any) {
    const message = JSON.stringify(data);
    clients.forEach(client => {
      if (client.readyState === WebSocket.OPEN) {
        client.send(message);
      }
    });
  }

  // Projects
  app.get("/api/projects", async (req, res) => {
    try {
      // For demo, use user ID 1
      const projects = await storage.getProjectsByUser(1);
      res.json(projects);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch projects" });
    }
  });

  app.get("/api/projects/:id", async (req, res) => {
    try {
      const project = await storage.getProject(parseInt(req.params.id));
      if (!project) {
        return res.status(404).json({ error: "Project not found" });
      }
      res.json(project);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch project" });
    }
  });

  app.post("/api/projects", async (req, res) => {
    try {
      const data = insertProjectSchema.parse({ ...req.body, userId: 1 });
      const project = await storage.createProject(data);
      broadcast({ type: 'project_created', project });
      res.json(project);
    } catch (error) {
      res.status(400).json({ error: "Invalid project data" });
    }
  });

  // Agents
  app.get("/api/projects/:projectId/agents", async (req, res) => {
    try {
      const agents = await storage.getAgentsByProject(parseInt(req.params.projectId));
      res.json(agents);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch agents" });
    }
  });

  app.post("/api/agents", async (req, res) => {
    try {
      const data = insertAgentSchema.parse(req.body);
      const agent = await storage.createAgent(data);
      broadcast({ type: 'agent_created', agent });
      res.json(agent);
    } catch (error) {
      res.status(400).json({ error: "Invalid agent data" });
    }
  });

  app.patch("/api/agents/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const agent = await storage.updateAgent(id, req.body);
      broadcast({ type: 'agent_updated', agent });
      res.json(agent);
    } catch (error) {
      res.status(400).json({ error: "Failed to update agent" });
    }
  });

  // Workflow
  app.get("/api/projects/:projectId/workflow/nodes", async (req, res) => {
    try {
      const nodes = await storage.getWorkflowNodesByProject(parseInt(req.params.projectId));
      res.json(nodes);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch workflow nodes" });
    }
  });

  app.get("/api/projects/:projectId/workflow/connections", async (req, res) => {
    try {
      const connections = await storage.getWorkflowConnectionsByProject(parseInt(req.params.projectId));
      res.json(connections);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch workflow connections" });
    }
  });

  app.post("/api/workflow/nodes", async (req, res) => {
    try {
      const data = insertWorkflowNodeSchema.parse(req.body);
      const node = await storage.createWorkflowNode(data);
      broadcast({ type: 'workflow_node_created', node });
      res.json(node);
    } catch (error) {
      res.status(400).json({ error: "Invalid workflow node data" });
    }
  });

  app.patch("/api/workflow/nodes/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const node = await storage.updateWorkflowNode(id, req.body);
      broadcast({ type: 'workflow_node_updated', node });
      res.json(node);
    } catch (error) {
      res.status(400).json({ error: "Failed to update workflow node" });
    }
  });

  app.post("/api/workflow/connections", async (req, res) => {
    try {
      const data = insertWorkflowConnectionSchema.parse(req.body);
      const connection = await storage.createWorkflowConnection(data);
      broadcast({ type: 'workflow_connection_created', connection });
      res.json(connection);
    } catch (error) {
      res.status(400).json({ error: "Invalid workflow connection data" });
    }
  });

  // Messages
  app.get("/api/projects/:projectId/messages", async (req, res) => {
    try {
      const limit = req.query.limit ? parseInt(req.query.limit as string) : 50;
      const messages = await storage.getMessagesByProject(parseInt(req.params.projectId), limit);
      res.json(messages);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch messages" });
    }
  });

  app.post("/api/messages", async (req, res) => {
    try {
      const data = insertMessageSchema.parse(req.body);
      const message = await storage.createMessage(data);
      broadcast({ type: 'message_created', message });
      res.json(message);
    } catch (error) {
      res.status(400).json({ error: "Invalid message data" });
    }
  });

  // Tasks
  app.get("/api/projects/:projectId/tasks", async (req, res) => {
    try {
      const tasks = await storage.getTasksByProject(parseInt(req.params.projectId));
      res.json(tasks);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch tasks" });
    }
  });

  app.post("/api/tasks", async (req, res) => {
    try {
      const data = insertTaskSchema.parse(req.body);
      const task = await storage.createTask(data);
      broadcast({ type: 'task_created', task });
      res.json(task);
    } catch (error) {
      res.status(400).json({ error: "Invalid task data" });
    }
  });

  app.patch("/api/tasks/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const task = await storage.updateTask(id, req.body);
      broadcast({ type: 'task_updated', task });
      res.json(task);
    } catch (error) {
      res.status(400).json({ error: "Failed to update task" });
    }
  });

  // Enhanced SambaNova AI Integration
  app.post("/api/ai/chat", async (req, res) => {
    try {
      const { message, agentId, projectId } = req.body;

      // Create user message
      const userMessage = await storage.createMessage({
        projectId,
        agentId: null,
        content: message,
        type: "message"
      });

      // Get agent context if specified
      let agent = null;
      if (agentId) {
        agent = await storage.getAgent(agentId);
      }

      // Generate AI response using SambaNova
      const aiResponse = await sambaNovaAI.generateAgentResponse(
        agent?.role || "assistant",
        `Project ID: ${projectId}`,
        message
      );

      const aiMessage = await storage.createMessage({
        projectId,
        agentId,
        content: aiResponse,
        type: "message"
      });

      // Learn from interaction
      await learningSystem.recordInteraction(projectId, agentId, message, aiResponse);

      broadcast({ type: 'message_created', message: userMessage });
      broadcast({ type: 'message_created', message: aiMessage });

      res.json({ userMessage, aiMessage });
    } catch (error) {
      console.error("AI chat error:", error);
      res.status(500).json({ error: "Failed to process AI chat" });
    }
  });

  // Enhanced workflow execution with CrewAI
  app.post("/api/projects/:projectId/run", async (req, res) => {
    try {
      const projectId = parseInt(req.params.projectId);

      // Get project and agents
      const project = await storage.getProject(projectId);
      const agents = await storage.getAgentsByProject(projectId);

      if (!project) {
        return res.status(404).json({ error: "Project not found" });
      }

      // Create CrewAI configuration
      const crewConfig = {
        agents: agents.map(agent => ({
          role: agent.role,
          goal: `Complete tasks for ${project.name}`,
          backstory: `You are a ${agent.role} working on ${project.name}`,
          capabilities: agent.config?.capabilities || []
        })),
        tasks: [],
        process: "hierarchical" as const
      };

      // Start crew orchestration
      const orchestrationResult = await crewOrchestrator.startCrew(projectId, crewConfig);

      // Update all agents to active status
      for (const agent of agents) {
        await storage.updateAgent(agent.id, { status: "active" });
      }

      // Create system message
      const message = await storage.createMessage({
        projectId,
        agentId: null,
        content: `Workflow execution started with CrewAI orchestration. ${orchestrationResult}`,
        type: "system"
      });

      broadcast({ type: 'workflow_started', projectId });
      broadcast({ type: 'message_created', message });

      res.json({ success: true, message: "Workflow started successfully", orchestrationId: orchestrationResult });
    } catch (error) {
      console.error("Workflow execution error:", error);
      res.status(500).json({ error: "Failed to start workflow" });
    }
  });

  // Advanced AI Features Routes

  // Code Generation
  app.post("/api/ai/generate-code", async (req, res) => {
    try {
      const { prompt, type, language, projectId, agentId } = req.body;

      const codeGeneration = await sambaNovaAI.generateCode(prompt, type, language);

      const result = await storage.createCodeGeneration({
        projectId,
        agentId,
        type,
        language,
        prompt,
        generatedCode: codeGeneration.code,
        quality: codeGeneration.quality,
        status: "generated"
      });

      broadcast({ type: 'code_generated', codeGeneration: result });
      res.json(result);
    } catch (error) {
      console.error("Code generation error:", error);
      res.status(500).json({ error: "Failed to generate code" });
    }
  });

  // Requirements Analysis
  app.post("/api/ai/analyze-requirements", async (req, res) => {
    try {
      const { requirements, projectId } = req.body;

      const analysis = await sambaNovaAI.analyzeRequirements(requirements);

      // Create tasks based on analysis
      const tasks = [];
      for (const taskData of analysis.tasks) {
        const task = await storage.createTask({
          projectId,
          agentId: taskData.agentId,
          title: taskData.title,
          description: taskData.description,
          priority: taskData.priority,
          estimatedHours: taskData.estimatedHours
        });
        tasks.push(task);
      }

      res.json({ tasks, clarifications: analysis.clarifications });
    } catch (error) {
      console.error("Requirements analysis error:", error);
      res.status(500).json({ error: "Failed to analyze requirements" });
    }
  });

  // Agent Conflict Negotiation
  app.post("/api/ai/negotiate-conflict", async (req, res) => {
    try {
      const { collaborationId, resolution } = req.body;

      const collaboration = await storage.updateAgentCollaboration(collaborationId, {
        status: "resolved",
        data: { resolution },
        resolvedAt: new Date()
      });

      broadcast({ type: 'conflict_resolved', collaboration });
      res.json(collaboration);
    } catch (error) {
      console.error("Conflict negotiation error:", error);
      res.status(500).json({ error: "Failed to negotiate conflict" });
    }
  });

  // App Deployment
  app.post("/api/ai/deploy-app", async (req, res) => {
    try {
      const { projectId, version } = req.body;

      const deployment = await deploymentManager.deployApp(projectId, version);

      const result = await storage.createAppDeployment({
        projectId,
        version,
        status: deployment.status,
        deploymentUrl: deployment.url,
        buildLogs: deployment.logs
      });

      broadcast({ type: 'app_deployed', deployment: result });
      res.json(result);
    } catch (error) {
      console.error("App deployment error:", error);
      res.status(500).json({ error: "Failed to deploy app" });
    }
  });

  // App Monitoring
  app.get("/api/ai/monitor-app/:deploymentId", async (req, res) => {
    try {
      const deploymentId = parseInt(req.params.deploymentId);

      const metrics = await analyticsEngine.getMetrics(deploymentId);
      const alerts = await analyticsEngine.getAlerts(deploymentId);

      res.json({ metrics, alerts });
    } catch (error) {
      console.error("App monitoring error:", error);
      res.status(500).json({ error: "Failed to monitor app" });
    }
  });

  // Self-Healing
  app.post("/api/ai/self-heal", async (req, res) => {
    try {
      const { deploymentId, issue } = req.body;

      const healingResult = await sambaNovaAI.selfHeal(deploymentId, issue);

      res.json(healingResult);
    } catch (error) {
      console.error("Self-healing error:", error);
      res.status(500).json({ error: "Failed to self-heal app" });
    }
  });

  // Performance Optimization
  app.post("/api/ai/optimize-performance/:projectId", async (req, res) => {
    try {
      const projectId = parseInt(req.params.projectId);

      const optimization = await sambaNovaAI.optimizePerformance(projectId);

      res.json(optimization);
    } catch (error) {
      console.error("Performance optimization error:", error);
      res.status(500).json({ error: "Failed to optimize performance" });
    }
  });

  // Asset Generation
  app.post("/api/ai/generate-assets", async (req, res) => {
    try {
      const { type, description, projectId } = req.body;

      const asset = await sambaNovaAI.generateAsset(type, description);

      res.json(asset);
    } catch (error) {
      console.error("Asset generation error:", error);
      res.status(500).json({ error: "Failed to generate asset" });
    }
  });

  // Issue Prediction
  app.get("/api/ai/predict-issues/:projectId", async (req, res) => {
    try {
      const projectId = parseInt(req.params.projectId);

      const predictions = await analyticsEngine.predictIssues(projectId);

      res.json(predictions);
    } catch (error) {
      console.error("Issue prediction error:", error);
      res.status(500).json({ error: "Failed to predict issues" });
    }
  });

  // Agent Insights
  app.get("/api/ai/agent-insights/:agentId", async (req, res) => {
    try {
      const agentId = parseInt(req.params.agentId);

      const insights = await learningSystem.getAgentInsights(agentId);

      res.json(insights);
    } catch (error) {
      console.error("Agent insights error:", error);
      res.status(500).json({ error: "Failed to get agent insights" });
    }
  });

  // Health check endpoint
  app.get("/health", (_req, res) => {
    res.json({
      status: "healthy",
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: process.env.npm_package_version || "1.0.0",
      environment: process.env.NODE_ENV || "development"
    });
  });

  return httpServer;
}
