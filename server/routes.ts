import type { Express } from "express";
import { createServer, type Server } from "http";
import { WebSocketServer, WebSocket } from "ws";
import { storage } from "./storage";
import { 
  insertProjectSchema, insertAgentSchema, insertWorkflowNodeSchema, 
  insertWorkflowConnectionSchema, insertMessageSchema, insertTaskSchema 
} from "@shared/schema";

export async function registerRoutes(app: Express): Promise<Server> {
  const httpServer = createServer(app);

  // WebSocket server for real-time updates
  const wss = new WebSocketServer({ server: httpServer, path: '/ws' });
  
  const clients = new Set<WebSocket>();

  wss.on('connection', (ws) => {
    clients.add(ws);
    
    ws.on('close', () => {
      clients.delete(ws);
    });

    ws.on('message', async (data) => {
      try {
        const message = JSON.parse(data.toString());
        
        if (message.type === 'join_project') {
          ws.send(JSON.stringify({ type: 'joined', projectId: message.projectId }));
        }
      } catch (error) {
        console.error('WebSocket message error:', error);
      }
    });
  });

  function broadcast(data: any) {
    const message = JSON.stringify(data);
    clients.forEach(client => {
      if (client.readyState === WebSocket.OPEN) {
        client.send(message);
      }
    });
  }

  // Projects
  app.get("/api/projects", async (req, res) => {
    try {
      // For demo, use user ID 1
      const projects = await storage.getProjectsByUser(1);
      res.json(projects);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch projects" });
    }
  });

  app.get("/api/projects/:id", async (req, res) => {
    try {
      const project = await storage.getProject(parseInt(req.params.id));
      if (!project) {
        return res.status(404).json({ error: "Project not found" });
      }
      res.json(project);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch project" });
    }
  });

  app.post("/api/projects", async (req, res) => {
    try {
      const data = insertProjectSchema.parse({ ...req.body, userId: 1 });
      const project = await storage.createProject(data);
      broadcast({ type: 'project_created', project });
      res.json(project);
    } catch (error) {
      res.status(400).json({ error: "Invalid project data" });
    }
  });

  // Agents
  app.get("/api/projects/:projectId/agents", async (req, res) => {
    try {
      const agents = await storage.getAgentsByProject(parseInt(req.params.projectId));
      res.json(agents);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch agents" });
    }
  });

  app.post("/api/agents", async (req, res) => {
    try {
      const data = insertAgentSchema.parse(req.body);
      const agent = await storage.createAgent(data);
      broadcast({ type: 'agent_created', agent });
      res.json(agent);
    } catch (error) {
      res.status(400).json({ error: "Invalid agent data" });
    }
  });

  app.patch("/api/agents/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const agent = await storage.updateAgent(id, req.body);
      broadcast({ type: 'agent_updated', agent });
      res.json(agent);
    } catch (error) {
      res.status(400).json({ error: "Failed to update agent" });
    }
  });

  // Workflow
  app.get("/api/projects/:projectId/workflow/nodes", async (req, res) => {
    try {
      const nodes = await storage.getWorkflowNodesByProject(parseInt(req.params.projectId));
      res.json(nodes);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch workflow nodes" });
    }
  });

  app.get("/api/projects/:projectId/workflow/connections", async (req, res) => {
    try {
      const connections = await storage.getWorkflowConnectionsByProject(parseInt(req.params.projectId));
      res.json(connections);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch workflow connections" });
    }
  });

  app.post("/api/workflow/nodes", async (req, res) => {
    try {
      const data = insertWorkflowNodeSchema.parse(req.body);
      const node = await storage.createWorkflowNode(data);
      broadcast({ type: 'workflow_node_created', node });
      res.json(node);
    } catch (error) {
      res.status(400).json({ error: "Invalid workflow node data" });
    }
  });

  app.patch("/api/workflow/nodes/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const node = await storage.updateWorkflowNode(id, req.body);
      broadcast({ type: 'workflow_node_updated', node });
      res.json(node);
    } catch (error) {
      res.status(400).json({ error: "Failed to update workflow node" });
    }
  });

  app.post("/api/workflow/connections", async (req, res) => {
    try {
      const data = insertWorkflowConnectionSchema.parse(req.body);
      const connection = await storage.createWorkflowConnection(data);
      broadcast({ type: 'workflow_connection_created', connection });
      res.json(connection);
    } catch (error) {
      res.status(400).json({ error: "Invalid workflow connection data" });
    }
  });

  // Messages
  app.get("/api/projects/:projectId/messages", async (req, res) => {
    try {
      const limit = req.query.limit ? parseInt(req.query.limit as string) : 50;
      const messages = await storage.getMessagesByProject(parseInt(req.params.projectId), limit);
      res.json(messages);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch messages" });
    }
  });

  app.post("/api/messages", async (req, res) => {
    try {
      const data = insertMessageSchema.parse(req.body);
      const message = await storage.createMessage(data);
      broadcast({ type: 'message_created', message });
      res.json(message);
    } catch (error) {
      res.status(400).json({ error: "Invalid message data" });
    }
  });

  // Tasks
  app.get("/api/projects/:projectId/tasks", async (req, res) => {
    try {
      const tasks = await storage.getTasksByProject(parseInt(req.params.projectId));
      res.json(tasks);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch tasks" });
    }
  });

  app.post("/api/tasks", async (req, res) => {
    try {
      const data = insertTaskSchema.parse(req.body);
      const task = await storage.createTask(data);
      broadcast({ type: 'task_created', task });
      res.json(task);
    } catch (error) {
      res.status(400).json({ error: "Invalid task data" });
    }
  });

  app.patch("/api/tasks/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const task = await storage.updateTask(id, req.body);
      broadcast({ type: 'task_updated', task });
      res.json(task);
    } catch (error) {
      res.status(400).json({ error: "Failed to update task" });
    }
  });

  // SambaNova AI Integration
  app.post("/api/ai/chat", async (req, res) => {
    try {
      const { message, agentId, projectId } = req.body;
      
      // Create user message
      const userMessage = await storage.createMessage({
        projectId,
        agentId: null,
        content: message,
        type: "message"
      });

      // Here you would integrate with SambaNova API
      // For now, we'll simulate an AI response
      const aiResponse = `As ${agentId ? 'an agent' : 'AI assistant'}, I understand your request: "${message}". I'll help you with this task.`;
      
      const aiMessage = await storage.createMessage({
        projectId,
        agentId,
        content: aiResponse,
        type: "message"
      });

      broadcast({ type: 'message_created', message: userMessage });
      broadcast({ type: 'message_created', message: aiMessage });

      res.json({ userMessage, aiMessage });
    } catch (error) {
      res.status(500).json({ error: "Failed to process AI chat" });
    }
  });

  // Run workflow
  app.post("/api/projects/:projectId/run", async (req, res) => {
    try {
      const projectId = parseInt(req.params.projectId);
      
      // Update all agents to active status
      const agents = await storage.getAgentsByProject(projectId);
      for (const agent of agents) {
        await storage.updateAgent(agent.id, { status: "active" });
      }

      // Create system message
      const message = await storage.createMessage({
        projectId,
        agentId: null,
        content: "Workflow execution started. All agents are now active.",
        type: "system"
      });

      broadcast({ type: 'workflow_started', projectId });
      broadcast({ type: 'message_created', message });

      res.json({ success: true, message: "Workflow started successfully" });
    } catch (error) {
      res.status(500).json({ error: "Failed to start workflow" });
    }
  });

  return httpServer;
}
