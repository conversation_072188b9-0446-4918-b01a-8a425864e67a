import { 
  users, projects, agents, workflowNodes, workflowConnections, messages, tasks,
  type User, type InsertUser, type Project, type InsertProject, 
  type Agent, type InsertAgent, type WorkflowNode, type InsertWorkflowNode,
  type WorkflowConnection, type InsertWorkflowConnection,
  type Message, type InsertMessage, type Task, type InsertTask
} from "@shared/schema";
import { db } from "./db";
import { eq } from "drizzle-orm";

export interface IStorage {
  // Users
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;

  // Projects
  getProject(id: number): Promise<Project | undefined>;
  getProjectsByUser(userId: number): Promise<Project[]>;
  createProject(project: InsertProject): Promise<Project>;
  updateProject(id: number, updates: Partial<Project>): Promise<Project>;

  // Agents
  getAgent(id: number): Promise<Agent | undefined>;
  getAgentsByProject(projectId: number): Promise<Agent[]>;
  createAgent(agent: InsertAgent): Promise<Agent>;
  updateAgent(id: number, updates: Partial<Agent>): Promise<Agent>;

  // Workflow
  getWorkflowNodesByProject(projectId: number): Promise<WorkflowNode[]>;
  createWorkflowNode(node: InsertWorkflowNode): Promise<WorkflowNode>;
  updateWorkflowNode(id: number, updates: Partial<WorkflowNode>): Promise<WorkflowNode>;
  deleteWorkflowNode(id: number): Promise<void>;

  getWorkflowConnectionsByProject(projectId: number): Promise<WorkflowConnection[]>;
  createWorkflowConnection(connection: InsertWorkflowConnection): Promise<WorkflowConnection>;
  deleteWorkflowConnection(id: number): Promise<void>;

  // Messages
  getMessagesByProject(projectId: number, limit?: number): Promise<Message[]>;
  createMessage(message: InsertMessage): Promise<Message>;

  // Tasks
  getTasksByProject(projectId: number): Promise<Task[]>;
  getTasksByAgent(agentId: number): Promise<Task[]>;
  createTask(task: InsertTask): Promise<Task>;
  updateTask(id: number, updates: Partial<Task>): Promise<Task>;
}

export class MemStorage implements IStorage {
  private users: Map<number, User> = new Map();
  private projects: Map<number, Project> = new Map();
  private agents: Map<number, Agent> = new Map();
  private workflowNodes: Map<number, WorkflowNode> = new Map();
  private workflowConnections: Map<number, WorkflowConnection> = new Map();
  private messages: Map<number, Message> = new Map();
  private tasks: Map<number, Task> = new Map();
  private currentId = 1;

  constructor() {
    this.initializeDefaultData();
  }

  private initializeDefaultData() {
    // Create default user
    const defaultUser: User = {
      id: 1,
      username: "personal",
      password: "password"
    };
    this.users.set(1, defaultUser);

    // Create default project
    const defaultProject: Project = {
      id: 1,
      name: "E-Commerce Platform",
      description: "Multi-agent collaboration for building an e-commerce platform",
      userId: 1,
      status: "active",
      createdAt: new Date()
    };
    this.projects.set(1, defaultProject);

    // Create default agents
    const defaultAgents: Agent[] = [
      {
        id: 1,
        projectId: 1,
        name: "Team Leader",
        role: "team_leader",
        status: "active",
        config: { capabilities: ["coordination", "planning", "task_assignment"] },
        lastActivity: new Date()
      },
      {
        id: 2,
        projectId: 1,
        name: "Product Manager",
        role: "product_manager",
        status: "active",
        config: { capabilities: ["requirements", "user_stories", "feature_specs"] },
        lastActivity: new Date()
      },
      {
        id: 3,
        projectId: 1,
        name: "Architect",
        role: "architect",
        status: "waiting",
        config: { capabilities: ["system_design", "tech_stack", "architecture"] },
        lastActivity: new Date()
      },
      {
        id: 4,
        projectId: 1,
        name: "Engineer",
        role: "engineer",
        status: "active",
        config: { capabilities: ["coding", "implementation", "testing"] },
        lastActivity: new Date()
      },
      {
        id: 5,
        projectId: 1,
        name: "Data Analyst",
        role: "data_analyst",
        status: "idle",
        config: { capabilities: ["data_analysis", "metrics", "reporting"] },
        lastActivity: new Date()
      }
    ];

    defaultAgents.forEach(agent => this.agents.set(agent.id, agent));

    // Create default workflow nodes
    const defaultNodes: WorkflowNode[] = [
      {
        id: 1,
        projectId: 1,
        type: "start",
        agentId: null,
        position: { x: 100, y: 50 },
        config: { label: "Project Start" }
      },
      {
        id: 2,
        projectId: 1,
        type: "agent",
        agentId: 1,
        position: { x: 400, y: 50 },
        config: { label: "Team Leader" }
      },
      {
        id: 3,
        projectId: 1,
        type: "agent",
        agentId: 2,
        position: { x: 150, y: 250 },
        config: { label: "Product Manager" }
      },
      {
        id: 4,
        projectId: 1,
        type: "agent",
        agentId: 3,
        position: { x: 450, y: 250 },
        config: { label: "Architect" }
      },
      {
        id: 5,
        projectId: 1,
        type: "agent",
        agentId: 4,
        position: { x: 300, y: 450 },
        config: { label: "Engineer" }
      }
    ];

    defaultNodes.forEach(node => this.workflowNodes.set(node.id, node));

    // Create default connections
    const defaultConnections: WorkflowConnection[] = [
      { id: 1, projectId: 1, sourceNodeId: 1, targetNodeId: 2 },
      { id: 2, projectId: 1, sourceNodeId: 2, targetNodeId: 3 },
      { id: 3, projectId: 1, sourceNodeId: 2, targetNodeId: 4 },
      { id: 4, projectId: 1, sourceNodeId: 3, targetNodeId: 5 },
      { id: 5, projectId: 1, sourceNodeId: 4, targetNodeId: 5 }
    ];

    defaultConnections.forEach(conn => this.workflowConnections.set(conn.id, conn));

    this.currentId = 10;
  }

  // Users
  async getUser(id: number): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(user => user.username === username);
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const id = this.currentId++;
    const user: User = { ...insertUser, id };
    this.users.set(id, user);
    return user;
  }

  // Projects
  async getProject(id: number): Promise<Project | undefined> {
    return this.projects.get(id);
  }

  async getProjectsByUser(userId: number): Promise<Project[]> {
    return Array.from(this.projects.values()).filter(project => project.userId === userId);
  }

  async createProject(insertProject: InsertProject): Promise<Project> {
    const id = this.currentId++;
    const project: Project = { 
      ...insertProject, 
      id, 
      status: "active",
      createdAt: new Date(),
      description: insertProject.description || null
    };
    this.projects.set(id, project);
    return project;
  }

  async updateProject(id: number, updates: Partial<Project>): Promise<Project> {
    const project = this.projects.get(id);
    if (!project) throw new Error("Project not found");
    const updated = { ...project, ...updates };
    this.projects.set(id, updated);
    return updated;
  }

  // Agents
  async getAgent(id: number): Promise<Agent | undefined> {
    return this.agents.get(id);
  }

  async getAgentsByProject(projectId: number): Promise<Agent[]> {
    return Array.from(this.agents.values()).filter(agent => agent.projectId === projectId);
  }

  async createAgent(insertAgent: InsertAgent): Promise<Agent> {
    const id = this.currentId++;
    const agent: Agent = { 
      ...insertAgent, 
      id, 
      status: "idle",
      config: insertAgent.config || {},
      lastActivity: new Date() 
    };
    this.agents.set(id, agent);
    return agent;
  }

  async updateAgent(id: number, updates: Partial<Agent>): Promise<Agent> {
    const agent = this.agents.get(id);
    if (!agent) throw new Error("Agent not found");
    const updated = { ...agent, ...updates, lastActivity: new Date() };
    this.agents.set(id, updated);
    return updated;
  }

  // Workflow
  async getWorkflowNodesByProject(projectId: number): Promise<WorkflowNode[]> {
    return Array.from(this.workflowNodes.values()).filter(node => node.projectId === projectId);
  }

  async createWorkflowNode(insertNode: InsertWorkflowNode): Promise<WorkflowNode> {
    const id = this.currentId++;
    const node: WorkflowNode = { 
      ...insertNode, 
      id,
      config: insertNode.config || {},
      agentId: insertNode.agentId || null
    };
    this.workflowNodes.set(id, node);
    return node;
  }

  async updateWorkflowNode(id: number, updates: Partial<WorkflowNode>): Promise<WorkflowNode> {
    const node = this.workflowNodes.get(id);
    if (!node) throw new Error("Workflow node not found");
    const updated = { ...node, ...updates };
    this.workflowNodes.set(id, updated);
    return updated;
  }

  async deleteWorkflowNode(id: number): Promise<void> {
    this.workflowNodes.delete(id);
  }

  async getWorkflowConnectionsByProject(projectId: number): Promise<WorkflowConnection[]> {
    return Array.from(this.workflowConnections.values()).filter(conn => conn.projectId === projectId);
  }

  async createWorkflowConnection(insertConnection: InsertWorkflowConnection): Promise<WorkflowConnection> {
    const id = this.currentId++;
    const connection: WorkflowConnection = { ...insertConnection, id };
    this.workflowConnections.set(id, connection);
    return connection;
  }

  async deleteWorkflowConnection(id: number): Promise<void> {
    this.workflowConnections.delete(id);
  }

  // Messages
  async getMessagesByProject(projectId: number, limit = 50): Promise<Message[]> {
    return Array.from(this.messages.values())
      .filter(message => message.projectId === projectId)
      .sort((a, b) => (b.timestamp?.getTime() || 0) - (a.timestamp?.getTime() || 0))
      .slice(0, limit)
      .reverse();
  }

  async createMessage(insertMessage: InsertMessage): Promise<Message> {
    const id = this.currentId++;
    const message: Message = { 
      ...insertMessage, 
      id, 
      type: insertMessage.type || "message",
      agentId: insertMessage.agentId || null,
      timestamp: new Date() 
    };
    this.messages.set(id, message);
    return message;
  }

  // Tasks
  async getTasksByProject(projectId: number): Promise<Task[]> {
    return Array.from(this.tasks.values()).filter(task => task.projectId === projectId);
  }

  async getTasksByAgent(agentId: number): Promise<Task[]> {
    return Array.from(this.tasks.values()).filter(task => task.agentId === agentId);
  }

  async createTask(insertTask: InsertTask): Promise<Task> {
    const id = this.currentId++;
    const task: Task = { 
      ...insertTask, 
      id, 
      status: "pending",
      description: insertTask.description || null,
      result: null,
      createdAt: new Date(),
      completedAt: null
    };
    this.tasks.set(id, task);
    return task;
  }

  async updateTask(id: number, updates: Partial<Task>): Promise<Task> {
    const task = this.tasks.get(id);
    if (!task) throw new Error("Task not found");
    const updated = { 
      ...task, 
      ...updates,
      completedAt: updates.status === "completed" ? new Date() : task.completedAt
    };
    this.tasks.set(id, updated);
    return updated;
  }
}

class DatabaseStorage implements IStorage {
  async getUser(id: number): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user || undefined;
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.username, username));
    return user || undefined;
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const [user] = await db
      .insert(users)
      .values(insertUser)
      .returning();
    return user;
  }

  async getProject(id: number): Promise<Project | undefined> {
    const [project] = await db.select().from(projects).where(eq(projects.id, id));
    return project || undefined;
  }

  async getProjectsByUser(userId: number): Promise<Project[]> {
    return await db.select().from(projects).where(eq(projects.userId, userId));
  }

  async createProject(insertProject: InsertProject): Promise<Project> {
    const [project] = await db
      .insert(projects)
      .values(insertProject)
      .returning();
    return project;
  }

  async updateProject(id: number, updates: Partial<Project>): Promise<Project> {
    const [project] = await db
      .update(projects)
      .set(updates)
      .where(eq(projects.id, id))
      .returning();
    return project;
  }

  async getAgent(id: number): Promise<Agent | undefined> {
    const [agent] = await db.select().from(agents).where(eq(agents.id, id));
    return agent || undefined;
  }

  async getAgentsByProject(projectId: number): Promise<Agent[]> {
    return await db.select().from(agents).where(eq(agents.projectId, projectId));
  }

  async createAgent(insertAgent: InsertAgent): Promise<Agent> {
    const [agent] = await db
      .insert(agents)
      .values(insertAgent)
      .returning();
    return agent;
  }

  async updateAgent(id: number, updates: Partial<Agent>): Promise<Agent> {
    const [agent] = await db
      .update(agents)
      .set(updates)
      .where(eq(agents.id, id))
      .returning();
    return agent;
  }

  async getWorkflowNodesByProject(projectId: number): Promise<WorkflowNode[]> {
    return await db.select().from(workflowNodes).where(eq(workflowNodes.projectId, projectId));
  }

  async createWorkflowNode(insertNode: InsertWorkflowNode): Promise<WorkflowNode> {
    const [node] = await db
      .insert(workflowNodes)
      .values(insertNode)
      .returning();
    return node;
  }

  async updateWorkflowNode(id: number, updates: Partial<WorkflowNode>): Promise<WorkflowNode> {
    const [node] = await db
      .update(workflowNodes)
      .set(updates)
      .where(eq(workflowNodes.id, id))
      .returning();
    return node;
  }

  async deleteWorkflowNode(id: number): Promise<void> {
    await db.delete(workflowNodes).where(eq(workflowNodes.id, id));
  }

  async getWorkflowConnectionsByProject(projectId: number): Promise<WorkflowConnection[]> {
    return await db.select().from(workflowConnections).where(eq(workflowConnections.projectId, projectId));
  }

  async createWorkflowConnection(insertConnection: InsertWorkflowConnection): Promise<WorkflowConnection> {
    const [connection] = await db
      .insert(workflowConnections)
      .values(insertConnection)
      .returning();
    return connection;
  }

  async deleteWorkflowConnection(id: number): Promise<void> {
    await db.delete(workflowConnections).where(eq(workflowConnections.id, id));
  }

  async getMessagesByProject(projectId: number, limit = 50): Promise<Message[]> {
    return await db.select().from(messages)
      .where(eq(messages.projectId, projectId))
      .orderBy(messages.timestamp)
      .limit(limit);
  }

  async createMessage(insertMessage: InsertMessage): Promise<Message> {
    const [message] = await db
      .insert(messages)
      .values(insertMessage)
      .returning();
    return message;
  }

  async getTasksByProject(projectId: number): Promise<Task[]> {
    return await db.select().from(tasks).where(eq(tasks.projectId, projectId));
  }

  async getTasksByAgent(agentId: number): Promise<Task[]> {
    return await db.select().from(tasks).where(eq(tasks.agentId, agentId));
  }

  async createTask(insertTask: InsertTask): Promise<Task> {
    const [task] = await db
      .insert(tasks)
      .values(insertTask)
      .returning();
    return task;
  }

  async updateTask(id: number, updates: Partial<Task>): Promise<Task> {
    const [task] = await db
      .update(tasks)
      .set(updates)
      .where(eq(tasks.id, id))
      .returning();
    return task;
  }
}

export const storage = new DatabaseStorage();
