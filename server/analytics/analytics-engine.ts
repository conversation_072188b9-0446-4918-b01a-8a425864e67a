interface MetricData {
  timestamp: Date;
  value: number;
  metadata?: Record<string, any>;
}

interface Alert {
  id: string;
  type: "performance" | "error" | "security" | "availability";
  severity: "low" | "medium" | "high" | "critical";
  message: string;
  timestamp: Date;
  resolved: boolean;
  deploymentId: number;
}

interface PerformanceMetrics {
  responseTime: MetricData[];
  throughput: MetricData[];
  errorRate: MetricData[];
  cpuUsage: MetricData[];
  memoryUsage: MetricData[];
  diskUsage: MetricData[];
}

interface PredictionResult {
  predictions: Array<{
    type: string;
    probability: number;
    timeframe: string;
    description: string;
    impact: "low" | "medium" | "high";
  }>;
  confidence: number;
  recommendations: string[];
}

export class AnalyticsEngine {
  private metrics: Map<number, PerformanceMetrics> = new Map();
  private alerts: Map<number, Alert[]> = new Map();
  private thresholds = {
    responseTime: 1000, // ms
    errorRate: 0.05, // 5%
    cpuUsage: 0.8, // 80%
    memoryUsage: 0.85, // 85%
    diskUsage: 0.9 // 90%
  };

  async recordMetric(
    deploymentId: number, 
    metric: string, 
    value: number, 
    metadata?: Record<string, any>
  ): Promise<void> {
    if (!this.metrics.has(deploymentId)) {
      this.metrics.set(deploymentId, {
        responseTime: [],
        throughput: [],
        errorRate: [],
        cpuUsage: [],
        memoryUsage: [],
        diskUsage: []
      });
    }

    const deploymentMetrics = this.metrics.get(deploymentId)!;
    const metricData: MetricData = {
      timestamp: new Date(),
      value,
      metadata
    };

    switch (metric) {
      case "response_time":
        deploymentMetrics.responseTime.push(metricData);
        this.checkResponseTimeThreshold(deploymentId, value);
        break;
      case "throughput":
        deploymentMetrics.throughput.push(metricData);
        break;
      case "error_rate":
        deploymentMetrics.errorRate.push(metricData);
        this.checkErrorRateThreshold(deploymentId, value);
        break;
      case "cpu_usage":
        deploymentMetrics.cpuUsage.push(metricData);
        this.checkCpuUsageThreshold(deploymentId, value);
        break;
      case "memory_usage":
        deploymentMetrics.memoryUsage.push(metricData);
        this.checkMemoryUsageThreshold(deploymentId, value);
        break;
      case "disk_usage":
        deploymentMetrics.diskUsage.push(metricData);
        this.checkDiskUsageThreshold(deploymentId, value);
        break;
    }

    // Keep only last 1000 data points per metric
    this.trimMetrics(deploymentMetrics);
  }

  async getMetrics(deploymentId: number, timeRange?: { start: Date; end: Date }): Promise<PerformanceMetrics | null> {
    const metrics = this.metrics.get(deploymentId);
    if (!metrics) return null;

    if (!timeRange) return metrics;

    // Filter metrics by time range
    const filteredMetrics: PerformanceMetrics = {
      responseTime: this.filterByTimeRange(metrics.responseTime, timeRange),
      throughput: this.filterByTimeRange(metrics.throughput, timeRange),
      errorRate: this.filterByTimeRange(metrics.errorRate, timeRange),
      cpuUsage: this.filterByTimeRange(metrics.cpuUsage, timeRange),
      memoryUsage: this.filterByTimeRange(metrics.memoryUsage, timeRange),
      diskUsage: this.filterByTimeRange(metrics.diskUsage, timeRange)
    };

    return filteredMetrics;
  }

  async getAlerts(deploymentId: number): Promise<Alert[]> {
    return this.alerts.get(deploymentId) || [];
  }

  async resolveAlert(alertId: string): Promise<boolean> {
    for (const [deploymentId, alerts] of this.alerts.entries()) {
      const alert = alerts.find(a => a.id === alertId);
      if (alert) {
        alert.resolved = true;
        return true;
      }
    }
    return false;
  }

  async predictIssues(projectId: number): Promise<PredictionResult> {
    // Simulate ML-based prediction
    const predictions = [];
    const deploymentMetrics = Array.from(this.metrics.values());

    // Analyze trends
    for (const metrics of deploymentMetrics) {
      // Response time trend analysis
      if (metrics.responseTime.length > 10) {
        const trend = this.calculateTrend(metrics.responseTime.slice(-10));
        if (trend > 0.1) {
          predictions.push({
            type: "performance_degradation",
            probability: Math.min(trend * 100, 95),
            timeframe: "next 24 hours",
            description: "Response time is trending upward, indicating potential performance issues",
            impact: "medium" as const
          });
        }
      }

      // Error rate analysis
      if (metrics.errorRate.length > 5) {
        const avgErrorRate = this.calculateAverage(metrics.errorRate.slice(-5));
        if (avgErrorRate > this.thresholds.errorRate * 0.8) {
          predictions.push({
            type: "error_spike",
            probability: 75,
            timeframe: "next 6 hours",
            description: "Error rate approaching threshold, potential system instability",
            impact: "high" as const
          });
        }
      }

      // Resource usage analysis
      if (metrics.cpuUsage.length > 5) {
        const avgCpuUsage = this.calculateAverage(metrics.cpuUsage.slice(-5));
        if (avgCpuUsage > this.thresholds.cpuUsage * 0.9) {
          predictions.push({
            type: "resource_exhaustion",
            probability: 80,
            timeframe: "next 12 hours",
            description: "CPU usage is very high, may lead to system overload",
            impact: "high" as const
          });
        }
      }
    }

    // Calculate overall confidence
    const confidence = predictions.length > 0 
      ? predictions.reduce((sum, p) => sum + p.probability, 0) / predictions.length / 100
      : 0.5;

    // Generate recommendations
    const recommendations = this.generateRecommendations(predictions);

    return {
      predictions,
      confidence,
      recommendations
    };
  }

  async generateInsights(deploymentId: number): Promise<any> {
    const metrics = this.metrics.get(deploymentId);
    if (!metrics) return null;

    const insights = {
      performance: this.analyzePerformance(metrics),
      reliability: this.analyzeReliability(metrics),
      efficiency: this.analyzeEfficiency(metrics),
      trends: this.analyzeTrends(metrics),
      recommendations: []
    };

    // Generate recommendations based on insights
    insights.recommendations = this.generateInsightRecommendations(insights);

    return insights;
  }

  async detectAnomalies(deploymentId: number): Promise<any[]> {
    const metrics = this.metrics.get(deploymentId);
    if (!metrics) return [];

    const anomalies = [];

    // Simple anomaly detection using statistical methods
    for (const [metricName, metricData] of Object.entries(metrics)) {
      if (metricData.length < 10) continue;

      const values = metricData.map(d => d.value);
      const mean = this.calculateAverage(metricData);
      const stdDev = this.calculateStandardDeviation(values, mean);
      const threshold = 2 * stdDev; // 2 standard deviations

      const recentValues = values.slice(-5);
      for (let i = 0; i < recentValues.length; i++) {
        if (Math.abs(recentValues[i] - mean) > threshold) {
          anomalies.push({
            metric: metricName,
            value: recentValues[i],
            expected: mean,
            deviation: Math.abs(recentValues[i] - mean),
            timestamp: metricData[metricData.length - 5 + i].timestamp,
            severity: this.calculateAnomalySeverity(recentValues[i], mean, threshold)
          });
        }
      }
    }

    return anomalies;
  }

  private createAlert(
    deploymentId: number,
    type: Alert["type"],
    severity: Alert["severity"],
    message: string
  ): void {
    const alert: Alert = {
      id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type,
      severity,
      message,
      timestamp: new Date(),
      resolved: false,
      deploymentId
    };

    if (!this.alerts.has(deploymentId)) {
      this.alerts.set(deploymentId, []);
    }

    this.alerts.get(deploymentId)!.push(alert);

    // Keep only last 100 alerts
    const alerts = this.alerts.get(deploymentId)!;
    if (alerts.length > 100) {
      alerts.splice(0, alerts.length - 100);
    }
  }

  private checkResponseTimeThreshold(deploymentId: number, value: number): void {
    if (value > this.thresholds.responseTime) {
      this.createAlert(
        deploymentId,
        "performance",
        value > this.thresholds.responseTime * 2 ? "high" : "medium",
        `Response time ${value}ms exceeds threshold of ${this.thresholds.responseTime}ms`
      );
    }
  }

  private checkErrorRateThreshold(deploymentId: number, value: number): void {
    if (value > this.thresholds.errorRate) {
      this.createAlert(
        deploymentId,
        "error",
        value > this.thresholds.errorRate * 2 ? "critical" : "high",
        `Error rate ${(value * 100).toFixed(2)}% exceeds threshold of ${(this.thresholds.errorRate * 100).toFixed(2)}%`
      );
    }
  }

  private checkCpuUsageThreshold(deploymentId: number, value: number): void {
    if (value > this.thresholds.cpuUsage) {
      this.createAlert(
        deploymentId,
        "performance",
        value > 0.95 ? "critical" : "high",
        `CPU usage ${(value * 100).toFixed(1)}% exceeds threshold of ${(this.thresholds.cpuUsage * 100).toFixed(1)}%`
      );
    }
  }

  private checkMemoryUsageThreshold(deploymentId: number, value: number): void {
    if (value > this.thresholds.memoryUsage) {
      this.createAlert(
        deploymentId,
        "performance",
        value > 0.95 ? "critical" : "high",
        `Memory usage ${(value * 100).toFixed(1)}% exceeds threshold of ${(this.thresholds.memoryUsage * 100).toFixed(1)}%`
      );
    }
  }

  private checkDiskUsageThreshold(deploymentId: number, value: number): void {
    if (value > this.thresholds.diskUsage) {
      this.createAlert(
        deploymentId,
        "availability",
        value > 0.98 ? "critical" : "high",
        `Disk usage ${(value * 100).toFixed(1)}% exceeds threshold of ${(this.thresholds.diskUsage * 100).toFixed(1)}%`
      );
    }
  }

  private trimMetrics(metrics: PerformanceMetrics): void {
    const maxDataPoints = 1000;
    
    for (const [key, data] of Object.entries(metrics)) {
      if (data.length > maxDataPoints) {
        data.splice(0, data.length - maxDataPoints);
      }
    }
  }

  private filterByTimeRange(data: MetricData[], timeRange: { start: Date; end: Date }): MetricData[] {
    return data.filter(d => d.timestamp >= timeRange.start && d.timestamp <= timeRange.end);
  }

  private calculateTrend(data: MetricData[]): number {
    if (data.length < 2) return 0;
    
    const values = data.map(d => d.value);
    const n = values.length;
    const sumX = (n * (n - 1)) / 2;
    const sumY = values.reduce((sum, val) => sum + val, 0);
    const sumXY = values.reduce((sum, val, i) => sum + i * val, 0);
    const sumX2 = (n * (n - 1) * (2 * n - 1)) / 6;
    
    const slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
    return slope;
  }

  private calculateAverage(data: MetricData[]): number {
    if (data.length === 0) return 0;
    return data.reduce((sum, d) => sum + d.value, 0) / data.length;
  }

  private calculateStandardDeviation(values: number[], mean: number): number {
    if (values.length === 0) return 0;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
    return Math.sqrt(variance);
  }

  private calculateAnomalySeverity(value: number, mean: number, threshold: number): string {
    const deviation = Math.abs(value - mean);
    if (deviation > threshold * 3) return "critical";
    if (deviation > threshold * 2) return "high";
    if (deviation > threshold) return "medium";
    return "low";
  }

  private analyzePerformance(metrics: PerformanceMetrics): any {
    return {
      avgResponseTime: this.calculateAverage(metrics.responseTime),
      avgThroughput: this.calculateAverage(metrics.throughput),
      avgErrorRate: this.calculateAverage(metrics.errorRate)
    };
  }

  private analyzeReliability(metrics: PerformanceMetrics): any {
    const uptime = metrics.errorRate.filter(d => d.value < 0.01).length / metrics.errorRate.length;
    return {
      uptime: uptime * 100,
      errorRate: this.calculateAverage(metrics.errorRate) * 100
    };
  }

  private analyzeEfficiency(metrics: PerformanceMetrics): any {
    return {
      avgCpuUsage: this.calculateAverage(metrics.cpuUsage) * 100,
      avgMemoryUsage: this.calculateAverage(metrics.memoryUsage) * 100,
      avgDiskUsage: this.calculateAverage(metrics.diskUsage) * 100
    };
  }

  private analyzeTrends(metrics: PerformanceMetrics): any {
    return {
      responseTimeTrend: this.calculateTrend(metrics.responseTime),
      throughputTrend: this.calculateTrend(metrics.throughput),
      errorRateTrend: this.calculateTrend(metrics.errorRate)
    };
  }

  private generateRecommendations(predictions: any[]): string[] {
    const recommendations = [];
    
    for (const prediction of predictions) {
      switch (prediction.type) {
        case "performance_degradation":
          recommendations.push("Consider scaling up resources or optimizing code");
          break;
        case "error_spike":
          recommendations.push("Review recent deployments and error logs");
          break;
        case "resource_exhaustion":
          recommendations.push("Scale horizontally or upgrade instance size");
          break;
      }
    }

    return recommendations;
  }

  private generateInsightRecommendations(insights: any): string[] {
    const recommendations = [];
    
    if (insights.performance.avgResponseTime > this.thresholds.responseTime) {
      recommendations.push("Optimize database queries and API responses");
    }
    
    if (insights.efficiency.avgCpuUsage > 70) {
      recommendations.push("Consider CPU optimization or scaling");
    }
    
    if (insights.reliability.uptime < 99) {
      recommendations.push("Implement better error handling and monitoring");
    }

    return recommendations;
  }
}
