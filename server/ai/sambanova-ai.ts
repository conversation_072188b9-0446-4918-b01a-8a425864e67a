import OpenAI from "openai";

interface CodeGenerationResult {
  code: string;
  quality: number;
  explanation: string;
}

interface RequirementsAnalysis {
  tasks: Array<{
    title: string;
    description: string;
    agentId: number;
    priority: string;
    estimatedHours: number;
  }>;
  clarifications: string[];
}

interface SelfHealingResult {
  success: boolean;
  actions: string[];
  diagnosis: string;
}

interface PerformanceOptimization {
  suggestions: string[];
  metrics: any;
  optimizedCode?: string;
}

interface AssetGeneration {
  url: string;
  metadata: any;
  type: string;
}

export class SambaNovaAI {
  private client: OpenAI;

  constructor() {
    this.client = new OpenAI({
      apiKey: process.env.SAMBANOVA_API_KEY || "",
      baseURL: "https://api.sambanova.ai/v1",
    });
  }

  async generateAgentResponse(role: string, context: string, message: string): Promise<string> {
    const systemPrompt = this.getAgentSystemPrompt(role);
    
    try {
      const response = await this.client.chat.completions.create({
        model: "Llama-4-Maverick-17B-128E-Instruct",
        messages: [
          { role: "system", content: systemPrompt },
          { role: "user", content: `Context: ${context}\n\nUser message: ${message}` }
        ],
        temperature: 0.1,
        top_p: 0.1,
      });

      return response.choices[0]?.message?.content || "I apologize, but I couldn't generate a response.";
    } catch (error) {
      console.error("SambaNova API error:", error);
      return `As a ${role}, I understand your request but I'm currently experiencing technical difficulties. Let me help you in a different way.`;
    }
  }

  async generateCode(prompt: string, type: string, language: string): Promise<CodeGenerationResult> {
    const codePrompt = `
Generate ${type} code in ${language} based on this prompt:
${prompt}

Requirements:
- Production-ready code with proper error handling
- Include comments and documentation
- Follow best practices and security guidelines
- Provide a quality score (1-10) and explanation

Format your response as:
CODE:
[your code here]

QUALITY: [score]
EXPLANATION: [explanation of the code and quality assessment]
`;

    try {
      const response = await this.client.chat.completions.create({
        model: "Llama-4-Maverick-17B-128E-Instruct",
        messages: [
          { role: "system", content: "You are an expert software developer capable of generating high-quality, production-ready code." },
          { role: "user", content: codePrompt }
        ],
        temperature: 0.2,
        top_p: 0.1,
      });

      const content = response.choices[0]?.message?.content || "";
      return this.parseCodeGeneration(content);
    } catch (error) {
      console.error("Code generation error:", error);
      return {
        code: `// Error generating code: ${error}`,
        quality: 1,
        explanation: "Failed to generate code due to technical issues."
      };
    }
  }

  async analyzeRequirements(requirements: string): Promise<RequirementsAnalysis> {
    const analysisPrompt = `
Analyze these project requirements and break them down into specific tasks:
${requirements}

For each task, provide:
1. Title
2. Description
3. Recommended agent type (1=team_leader, 2=product_manager, 3=architect, 4=engineer, 5=data_analyst)
4. Priority (low, medium, high, critical)
5. Estimated hours

Also identify any clarifications needed from the user.

Format your response as JSON:
{
  "tasks": [
    {
      "title": "Task title",
      "description": "Detailed description",
      "agentId": 1,
      "priority": "high",
      "estimatedHours": 8
    }
  ],
  "clarifications": ["What specific...", "How should..."]
}
`;

    try {
      const response = await this.client.chat.completions.create({
        model: "Llama-4-Maverick-17B-128E-Instruct",
        messages: [
          { role: "system", content: "You are an expert project manager and business analyst." },
          { role: "user", content: analysisPrompt }
        ],
        temperature: 0.1,
        top_p: 0.1,
      });

      const content = response.choices[0]?.message?.content || "";
      return this.parseRequirementsAnalysis(content);
    } catch (error) {
      console.error("Requirements analysis error:", error);
      return {
        tasks: [],
        clarifications: ["Unable to analyze requirements due to technical issues."]
      };
    }
  }

  async selfHeal(deploymentId: number, issue: string): Promise<SelfHealingResult> {
    const healingPrompt = `
Analyze this deployment issue and provide self-healing actions:
Deployment ID: ${deploymentId}
Issue: ${issue}

Provide:
1. Diagnosis of the problem
2. Specific actions to fix the issue
3. Whether the fix was successful

Format as JSON:
{
  "success": true/false,
  "actions": ["action1", "action2"],
  "diagnosis": "Root cause analysis"
}
`;

    try {
      const response = await this.client.chat.completions.create({
        model: "Llama-4-Maverick-17B-128E-Instruct",
        messages: [
          { role: "system", content: "You are an expert DevOps engineer specializing in automated issue resolution." },
          { role: "user", content: healingPrompt }
        ],
        temperature: 0.1,
        top_p: 0.1,
      });

      const content = response.choices[0]?.message?.content || "";
      return this.parseSelfHealingResult(content);
    } catch (error) {
      console.error("Self-healing error:", error);
      return {
        success: false,
        actions: ["Manual intervention required"],
        diagnosis: "Unable to automatically diagnose the issue."
      };
    }
  }

  async optimizePerformance(projectId: number): Promise<PerformanceOptimization> {
    const optimizationPrompt = `
Analyze and provide performance optimization suggestions for project ${projectId}.

Consider:
- Code efficiency
- Database optimization
- Caching strategies
- Resource utilization
- Scalability improvements

Provide specific, actionable suggestions.
`;

    try {
      const response = await this.client.chat.completions.create({
        model: "Llama-4-Maverick-17B-128E-Instruct",
        messages: [
          { role: "system", content: "You are a performance optimization expert." },
          { role: "user", content: optimizationPrompt }
        ],
        temperature: 0.1,
        top_p: 0.1,
      });

      const content = response.choices[0]?.message?.content || "";
      return this.parseOptimizationResult(content);
    } catch (error) {
      console.error("Performance optimization error:", error);
      return {
        suggestions: ["Unable to generate optimization suggestions"],
        metrics: {}
      };
    }
  }

  async generateAsset(type: string, description: string): Promise<AssetGeneration> {
    // For now, return a placeholder since actual asset generation would require additional services
    return {
      url: `https://placeholder.com/${type}`,
      metadata: {
        type,
        description,
        generated: new Date().toISOString()
      },
      type
    };
  }

  private getAgentSystemPrompt(role: string): string {
    const prompts = {
      team_leader: "You are a Team Leader AI agent responsible for coordinating project workflows, assigning tasks, and ensuring team alignment. You excel at strategic planning and team management. You can negotiate conflicts, allocate resources, and make executive decisions.",
      product_manager: "You are a Product Manager AI agent focused on analyzing requirements, creating user stories, defining features, and managing product roadmaps. You understand user needs and business objectives. You can clarify ambiguous requirements and prioritize features.",
      architect: "You are a Software Architect AI agent specializing in system design, technical architecture, technology stack decisions, and scalability planning. You think in terms of high-level system structure. You can design databases, APIs, and system integrations.",
      engineer: "You are a Software Engineer AI agent skilled in coding, implementation, testing, and technical problem-solving. You focus on writing clean, efficient code and building robust solutions. You can generate code, fix bugs, and optimize performance.",
      data_analyst: "You are a Data Analyst AI agent expert in data analysis, metrics, reporting, and insights generation. You help teams make data-driven decisions. You can analyze performance data and predict issues."
    };

    return prompts[role as keyof typeof prompts] || "You are a helpful AI assistant.";
  }

  private parseCodeGeneration(content: string): CodeGenerationResult {
    const codeMatch = content.match(/CODE:\s*([\s\S]*?)(?=QUALITY:|$)/);
    const qualityMatch = content.match(/QUALITY:\s*(\d+)/);
    const explanationMatch = content.match(/EXPLANATION:\s*([\s\S]*?)$/);

    return {
      code: codeMatch?.[1]?.trim() || content,
      quality: parseInt(qualityMatch?.[1] || "5"),
      explanation: explanationMatch?.[1]?.trim() || "Code generated successfully."
    };
  }

  private parseRequirementsAnalysis(content: string): RequirementsAnalysis {
    try {
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
    } catch (error) {
      console.error("Failed to parse requirements analysis:", error);
    }

    return {
      tasks: [],
      clarifications: ["Unable to parse requirements analysis."]
    };
  }

  private parseSelfHealingResult(content: string): SelfHealingResult {
    try {
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
    } catch (error) {
      console.error("Failed to parse self-healing result:", error);
    }

    return {
      success: false,
      actions: ["Manual intervention required"],
      diagnosis: "Unable to parse healing result."
    };
  }

  private parseOptimizationResult(content: string): PerformanceOptimization {
    const suggestions = content.match(/- (.*)/g)?.map(item => item.replace('- ', '')) || [];
    
    return {
      suggestions,
      metrics: {
        analyzed: new Date().toISOString(),
        suggestions_count: suggestions.length
      }
    };
  }
}
