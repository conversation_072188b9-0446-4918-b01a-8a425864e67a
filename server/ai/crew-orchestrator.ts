import { <PERSON><PERSON>NovaA<PERSON> } from "./sambanova-ai";

interface CrewAgent {
  role: string;
  goal: string;
  backstory: string;
  capabilities: string[];
}

interface CrewTask {
  id: string;
  description: string;
  agent: string;
  dependencies: string[];
  status: "pending" | "in_progress" | "completed" | "failed";
  result?: string;
}

interface CrewConfig {
  agents: CrewAgent[];
  tasks: CrewTask[];
  process: "sequential" | "hierarchical" | "consensus";
}

interface CrewExecution {
  id: string;
  projectId: number;
  status: "running" | "completed" | "failed";
  results: Record<string, any>;
  startTime: Date;
  endTime?: Date;
}

export class CrewAIOrchestrator {
  private sambaNovaAI: SambaNovaAI;
  private activeCrews: Map<string, CrewExecution> = new Map();

  constructor(sambaNovaAI: SambaNovaAI) {
    this.sambaNovaAI = sambaNovaAI;
  }

  async startCrew(projectId: number, config: CrewConfig): Promise<string> {
    const crewId = `crew_${projectId}_${Date.now()}`;
    
    const execution: CrewExecution = {
      id: crewId,
      projectId,
      status: "running",
      results: {},
      startTime: new Date()
    };

    this.activeCrews.set(crewId, execution);

    // Start crew execution in background
    this.executeCrew(crewId, config).catch(error => {
      console.error(`Crew ${crewId} execution failed:`, error);
      execution.status = "failed";
      execution.endTime = new Date();
    });

    return crewId;
  }

  async getCrewStatus(crewId: string): Promise<CrewExecution | null> {
    return this.activeCrews.get(crewId) || null;
  }

  async stopCrew(crewId: string): Promise<boolean> {
    const crew = this.activeCrews.get(crewId);
    if (crew && crew.status === "running") {
      crew.status = "failed";
      crew.endTime = new Date();
      return true;
    }
    return false;
  }

  private async executeCrew(crewId: string, config: CrewConfig): Promise<void> {
    const execution = this.activeCrews.get(crewId);
    if (!execution) return;

    try {
      switch (config.process) {
        case "sequential":
          await this.executeSequential(execution, config);
          break;
        case "hierarchical":
          await this.executeHierarchical(execution, config);
          break;
        case "consensus":
          await this.executeConsensus(execution, config);
          break;
      }

      execution.status = "completed";
      execution.endTime = new Date();
    } catch (error) {
      console.error(`Crew execution error:`, error);
      execution.status = "failed";
      execution.endTime = new Date();
    }
  }

  private async executeSequential(execution: CrewExecution, config: CrewConfig): Promise<void> {
    const tasks = this.orderTasksByDependencies(config.tasks);
    
    for (const task of tasks) {
      await this.executeTask(execution, task, config.agents);
    }
  }

  private async executeHierarchical(execution: CrewExecution, config: CrewConfig): Promise<void> {
    // Find team leader
    const teamLeader = config.agents.find(agent => agent.role === "team_leader");
    if (!teamLeader) {
      throw new Error("Hierarchical process requires a team leader");
    }

    // Team leader coordinates task assignment
    const coordinationPrompt = `
As the team leader, coordinate the following agents and tasks:
Agents: ${JSON.stringify(config.agents, null, 2)}
Tasks: ${JSON.stringify(config.tasks, null, 2)}

Assign tasks to appropriate agents and determine execution order.
Provide your coordination plan.
`;

    const coordinationPlan = await this.sambaNovaAI.generateAgentResponse(
      "team_leader",
      `Project: ${execution.projectId}`,
      coordinationPrompt
    );

    execution.results.coordinationPlan = coordinationPlan;

    // Execute tasks based on coordination plan
    const orderedTasks = this.orderTasksByDependencies(config.tasks);
    for (const task of orderedTasks) {
      await this.executeTask(execution, task, config.agents);
    }
  }

  private async executeConsensus(execution: CrewExecution, config: CrewConfig): Promise<void> {
    // All agents collaborate on each task
    for (const task of config.tasks) {
      const consensusResults = [];
      
      // Get input from all relevant agents
      for (const agent of config.agents) {
        const agentInput = await this.sambaNovaAI.generateAgentResponse(
          agent.role,
          `Task: ${task.description}`,
          `Provide your perspective on this task as a ${agent.role}.`
        );
        
        consensusResults.push({
          agent: agent.role,
          input: agentInput
        });
      }

      // Synthesize consensus
      const consensusPrompt = `
Based on the following agent inputs, synthesize a consensus solution:
${consensusResults.map(r => `${r.agent}: ${r.input}`).join('\n\n')}

Provide a unified solution that incorporates the best aspects of each perspective.
`;

      const consensusSolution = await this.sambaNovaAI.generateAgentResponse(
        "team_leader",
        `Task: ${task.description}`,
        consensusPrompt
      );

      task.result = consensusSolution;
      task.status = "completed";
      
      execution.results[task.id] = {
        agentInputs: consensusResults,
        consensusSolution
      };
    }
  }

  private async executeTask(execution: CrewExecution, task: CrewTask, agents: CrewAgent[]): Promise<void> {
    const assignedAgent = agents.find(agent => agent.role === task.agent);
    if (!assignedAgent) {
      throw new Error(`No agent found for role: ${task.agent}`);
    }

    // Check dependencies
    const dependencyResults = task.dependencies.map(depId => 
      execution.results[depId]?.result || ""
    ).join("\n");

    const taskPrompt = `
Execute this task: ${task.description}
${dependencyResults ? `Dependencies completed: ${dependencyResults}` : ""}

Provide a detailed result of your work.
`;

    task.status = "in_progress";
    
    try {
      const result = await this.sambaNovaAI.generateAgentResponse(
        assignedAgent.role,
        `Project: ${execution.projectId}`,
        taskPrompt
      );

      task.result = result;
      task.status = "completed";
      
      execution.results[task.id] = {
        agent: assignedAgent.role,
        result,
        completedAt: new Date()
      };
    } catch (error) {
      task.status = "failed";
      execution.results[task.id] = {
        agent: assignedAgent.role,
        error: error.message,
        failedAt: new Date()
      };
    }
  }

  private orderTasksByDependencies(tasks: CrewTask[]): CrewTask[] {
    const ordered: CrewTask[] = [];
    const remaining = [...tasks];
    const completed = new Set<string>();

    while (remaining.length > 0) {
      const readyTasks = remaining.filter(task => 
        task.dependencies.every(dep => completed.has(dep))
      );

      if (readyTasks.length === 0) {
        // Circular dependency or missing dependency
        console.warn("Circular dependency detected, adding remaining tasks");
        ordered.push(...remaining);
        break;
      }

      for (const task of readyTasks) {
        ordered.push(task);
        completed.add(task.id);
        const index = remaining.indexOf(task);
        remaining.splice(index, 1);
      }
    }

    return ordered;
  }

  async negotiateConflict(
    agentA: string, 
    agentB: string, 
    conflict: string, 
    context: string
  ): Promise<string> {
    const negotiationPrompt = `
There is a conflict between ${agentA} and ${agentB}:
Conflict: ${conflict}
Context: ${context}

As a neutral mediator, help resolve this conflict by:
1. Understanding both perspectives
2. Finding common ground
3. Proposing a solution that satisfies both agents' core needs

Provide a detailed resolution plan.
`;

    return await this.sambaNovaAI.generateAgentResponse(
      "team_leader",
      context,
      negotiationPrompt
    );
  }

  async optimizeWorkflow(projectId: number, currentWorkflow: any): Promise<any> {
    const optimizationPrompt = `
Analyze and optimize this workflow:
${JSON.stringify(currentWorkflow, null, 2)}

Suggest improvements for:
- Task parallelization
- Resource allocation
- Dependency optimization
- Agent specialization

Provide an optimized workflow configuration.
`;

    const optimization = await this.sambaNovaAI.generateAgentResponse(
      "team_leader",
      `Project: ${projectId}`,
      optimizationPrompt
    );

    return {
      originalWorkflow: currentWorkflow,
      optimizedWorkflow: optimization,
      improvements: this.extractImprovements(optimization)
    };
  }

  private extractImprovements(optimization: string): string[] {
    const improvements = optimization.match(/- (.*)/g);
    return improvements?.map(item => item.replace('- ', '')) || [];
  }

  async getCrewInsights(crewId: string): Promise<any> {
    const execution = this.activeCrews.get(crewId);
    if (!execution) return null;

    const duration = execution.endTime 
      ? execution.endTime.getTime() - execution.startTime.getTime()
      : Date.now() - execution.startTime.getTime();

    const completedTasks = Object.values(execution.results).filter(
      result => !result.error
    ).length;

    const failedTasks = Object.values(execution.results).filter(
      result => result.error
    ).length;

    return {
      crewId,
      projectId: execution.projectId,
      status: execution.status,
      duration: Math.round(duration / 1000), // seconds
      completedTasks,
      failedTasks,
      efficiency: completedTasks / (completedTasks + failedTasks) || 0,
      results: execution.results
    };
  }
}
