# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/buildmaster"

# SambaNova AI Configuration
SAMBANOVA_API_KEY="your_sambanova_api_key_here"
SAMBANOVA_BASE_URL="https://api.sambanova.ai/v1"
SAMBANOVA_MODEL="Llama-4-Maverick-17B-128E-Instruct"

# CrewAI Configuration
CREWAI_API_KEY="your_crewai_api_key_here"
CREWAI_BASE_URL="https://api.crewai.com/v1"

# LangChain Configuration
LANGCHAIN_API_KEY="your_langchain_api_key_here"
LANGCHAIN_TRACING_V2="true"
LANGCHAIN_PROJECT="buildmaster"

# Deployment Configuration
DOCKER_REGISTRY="your_docker_registry"
VERCEL_TOKEN="your_vercel_token"
NETLIFY_TOKEN="your_netlify_token"

# Analytics Configuration
ANALYTICS_API_KEY="your_analytics_api_key"
MONITORING_WEBHOOK_URL="your_monitoring_webhook_url"

# Security Configuration
JWT_SECRET="your_jwt_secret_key_here"
ENCRYPTION_KEY="your_encryption_key_here"

# Application Configuration
NODE_ENV="development"
PORT=3000
CLIENT_URL="http://localhost:5173"
SERVER_URL="http://localhost:3000"

# File Storage Configuration
STORAGE_TYPE="local" # local, s3, gcs
AWS_ACCESS_KEY_ID="your_aws_access_key"
AWS_SECRET_ACCESS_KEY="your_aws_secret_key"
AWS_REGION="us-east-1"
AWS_S3_BUCKET="buildmaster-storage"

# Redis Configuration (for caching and sessions)
REDIS_URL="redis://localhost:6379"

# Email Configuration
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_USER="<EMAIL>"
SMTP_PASS="your_email_password"

# Webhook Configuration
GITHUB_WEBHOOK_SECRET="your_github_webhook_secret"
SLACK_WEBHOOK_URL="your_slack_webhook_url"

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000 # 15 minutes
RATE_LIMIT_MAX_REQUESTS=100

# Logging Configuration
LOG_LEVEL="info"
LOG_FORMAT="json"

# Feature Flags
ENABLE_AI_FEATURES="true"
ENABLE_COLLABORATION="true"
ENABLE_CODE_GENERATION="true"
ENABLE_LIVE_PREVIEW="true"
ENABLE_ANALYTICS="true"
ENABLE_LEARNING_SYSTEM="true"
ENABLE_AUTO_DEPLOYMENT="true"
ENABLE_SELF_HEALING="true"

# Development Configuration
VITE_API_URL="http://localhost:3000"
VITE_WS_URL="ws://localhost:3000"
VITE_ENABLE_DEVTOOLS="true"
