# BuildMaster Deployment Guide

## Quick Start

### Prerequisites
- Node.js 18+
- PostgreSQL 14+
- Docker & Docker Compose (optional)
- SambaNova API key

### Local Development Setup

1. **Clone and Install**
   ```bash
   git clone https://github.com/your-username/buildmaster.git
   cd buildmaster
   npm run setup
   ```

2. **Environment Configuration**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Database Setup**
   ```bash
   # Start PostgreSQL (if using Docker)
   docker run -d --name postgres \
     -e POSTGRES_DB=buildmaster \
     -e POSTGRES_USER=buildmaster \
     -e POSTGRES_PASSWORD=password \
     -p 5432:5432 postgres:15-alpine

   # Run migrations
   npm run db:generate
   npm run db:migrate
   ```

4. **Start Development Server**
   ```bash
   npm run dev
   ```

   Access the application:
   - Frontend: http://localhost:5173
   - Backend: http://localhost:3000
   - Health Check: http://localhost:3000/health

## Docker Deployment

### Development with Docker

```bash
# Build and start all services
npm run docker:build
npm run docker:up

# View logs
npm run docker:logs

# Stop services
npm run docker:down
```

### Production Docker Deployment

1. **Prepare Environment**
   ```bash
   # Copy and configure production environment
   cp .env.example .env.production
   # Edit .env.production with production values
   ```

2. **Build Production Images**
   ```bash
   docker-compose -f docker-compose.yml build
   ```

3. **Deploy Services**
   ```bash
   docker-compose -f docker-compose.yml up -d
   ```

4. **Verify Deployment**
   ```bash
   # Check service health
   curl http://localhost/health
   
   # View service status
   docker-compose ps
   ```

## Environment Configuration

### Required Variables

```bash
# Database
DATABASE_URL="postgresql://user:password@localhost:5432/buildmaster"

# AI Services
SAMBANOVA_API_KEY="your_sambanova_api_key"

# Application
NODE_ENV="production"
PORT=3000
CLIENT_URL="https://your-domain.com"
```

### Optional Variables

```bash
# Advanced AI Features
CREWAI_API_KEY="your_crewai_key"
LANGCHAIN_API_KEY="your_langchain_key"

# Deployment Services
DOCKER_REGISTRY="your-registry.com"
VERCEL_TOKEN="your_vercel_token"

# Monitoring
ANALYTICS_API_KEY="your_analytics_key"
MONITORING_WEBHOOK_URL="your_webhook_url"

# Security
JWT_SECRET="your_jwt_secret"
ENCRYPTION_KEY="your_encryption_key"
```

## Cloud Deployment

### Vercel (Frontend)

1. **Install Vercel CLI**
   ```bash
   npm install -g vercel
   ```

2. **Deploy Frontend**
   ```bash
   cd client
   vercel --prod
   ```

3. **Configure Environment Variables**
   ```bash
   vercel env add VITE_API_URL production
   vercel env add VITE_WS_URL production
   ```

### Railway (Backend)

1. **Install Railway CLI**
   ```bash
   npm install -g @railway/cli
   ```

2. **Deploy Backend**
   ```bash
   railway login
   railway init
   railway up
   ```

3. **Configure Database**
   ```bash
   railway add postgresql
   railway run npm run db:migrate
   ```

### AWS ECS (Full Stack)

1. **Build and Push Images**
   ```bash
   # Build images
   docker build -f Dockerfile.backend -t buildmaster-backend .
   docker build -f Dockerfile.frontend -t buildmaster-frontend .

   # Tag for ECR
   docker tag buildmaster-backend:latest 123456789.dkr.ecr.region.amazonaws.com/buildmaster-backend:latest
   docker tag buildmaster-frontend:latest 123456789.dkr.ecr.region.amazonaws.com/buildmaster-frontend:latest

   # Push to ECR
   aws ecr get-login-password --region region | docker login --username AWS --password-stdin 123456789.dkr.ecr.region.amazonaws.com
   docker push 123456789.dkr.ecr.region.amazonaws.com/buildmaster-backend:latest
   docker push 123456789.dkr.ecr.region.amazonaws.com/buildmaster-frontend:latest
   ```

2. **Create ECS Task Definition**
   ```json
   {
     "family": "buildmaster",
     "networkMode": "awsvpc",
     "requiresCompatibilities": ["FARGATE"],
     "cpu": "512",
     "memory": "1024",
     "containerDefinitions": [
       {
         "name": "backend",
         "image": "123456789.dkr.ecr.region.amazonaws.com/buildmaster-backend:latest",
         "portMappings": [{"containerPort": 3000}],
         "environment": [
           {"name": "NODE_ENV", "value": "production"},
           {"name": "DATABASE_URL", "value": "your_database_url"}
         ]
       }
     ]
   }
   ```

### Google Cloud Run

1. **Build and Deploy**
   ```bash
   # Build and push to Google Container Registry
   gcloud builds submit --tag gcr.io/PROJECT_ID/buildmaster-backend
   gcloud builds submit --tag gcr.io/PROJECT_ID/buildmaster-frontend

   # Deploy to Cloud Run
   gcloud run deploy buildmaster-backend \
     --image gcr.io/PROJECT_ID/buildmaster-backend \
     --platform managed \
     --region us-central1 \
     --allow-unauthenticated

   gcloud run deploy buildmaster-frontend \
     --image gcr.io/PROJECT_ID/buildmaster-frontend \
     --platform managed \
     --region us-central1 \
     --allow-unauthenticated
   ```

## Database Management

### Migrations

```bash
# Generate migration files
npm run db:generate

# Apply migrations
npm run db:migrate

# View database in Drizzle Studio
npm run db:studio
```

### Backup and Restore

```bash
# Backup database
pg_dump $DATABASE_URL > backup.sql

# Restore database
psql $DATABASE_URL < backup.sql
```

## Monitoring and Logging

### Health Checks

```bash
# Application health
curl http://localhost:3000/health

# Database connectivity
curl http://localhost:3000/api/health/db

# AI services status
curl http://localhost:3000/api/health/ai
```

### Prometheus Metrics

Access metrics at: http://localhost:9090

Key metrics to monitor:
- `http_requests_total` - Total HTTP requests
- `http_request_duration_seconds` - Request latency
- `ai_generation_requests_total` - AI generation requests
- `agent_collaboration_active` - Active collaborations

### Grafana Dashboards

Access dashboards at: http://localhost:3002 (admin/admin)

Pre-configured dashboards:
- Application Performance
- AI Service Metrics
- Agent Collaboration Stats
- System Resource Usage

## Security Considerations

### Production Security Checklist

- [ ] Use HTTPS in production
- [ ] Configure proper CORS settings
- [ ] Set secure session cookies
- [ ] Enable rate limiting
- [ ] Use environment variables for secrets
- [ ] Regular security updates
- [ ] Database connection encryption
- [ ] API key rotation

### SSL/TLS Configuration

```nginx
server {
    listen 443 ssl http2;
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
}
```

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   ```bash
   # Check database status
   docker-compose logs postgres
   
   # Verify connection string
   echo $DATABASE_URL
   ```

2. **AI Service Timeout**
   ```bash
   # Check API key configuration
   echo $SAMBANOVA_API_KEY
   
   # Test API connectivity
   curl -H "Authorization: Bearer $SAMBANOVA_API_KEY" https://api.sambanova.ai/v1/models
   ```

3. **Build Failures**
   ```bash
   # Clear build cache
   docker system prune -a
   
   # Rebuild from scratch
   docker-compose build --no-cache
   ```

### Performance Optimization

1. **Database Optimization**
   ```sql
   -- Add indexes for frequently queried columns
   CREATE INDEX idx_projects_user_id ON projects(user_id);
   CREATE INDEX idx_agents_project_id ON agents(project_id);
   ```

2. **Application Optimization**
   ```bash
   # Enable production optimizations
   export NODE_ENV=production
   export VITE_BUILD_OPTIMIZE=true
   ```

3. **Container Optimization**
   ```dockerfile
   # Use multi-stage builds
   FROM node:18-alpine AS builder
   # ... build steps
   FROM node:18-alpine AS runtime
   # ... runtime setup
   ```

## Scaling Strategies

### Horizontal Scaling

1. **Load Balancer Configuration**
   ```nginx
   upstream backend {
       server backend1:3000;
       server backend2:3000;
       server backend3:3000;
   }
   ```

2. **Database Scaling**
   - Read replicas for analytics queries
   - Connection pooling with PgBouncer
   - Partitioning for large tables

3. **Caching Strategy**
   - Redis for session storage
   - CDN for static assets
   - Application-level caching

### Auto-scaling

```yaml
# Kubernetes HPA example
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: buildmaster-backend
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: buildmaster-backend
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
```

## Maintenance

### Regular Tasks

1. **Weekly**
   - Review application logs
   - Check system metrics
   - Update dependencies
   - Backup database

2. **Monthly**
   - Security audit
   - Performance review
   - Capacity planning
   - Documentation updates

3. **Quarterly**
   - Disaster recovery testing
   - Security penetration testing
   - Architecture review
   - Technology stack updates

### Backup Strategy

```bash
#!/bin/bash
# Automated backup script
DATE=$(date +%Y%m%d_%H%M%S)
pg_dump $DATABASE_URL | gzip > "backup_${DATE}.sql.gz"
aws s3 cp "backup_${DATE}.sql.gz" s3://your-backup-bucket/
```
