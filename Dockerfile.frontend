# Frontend Dockerfile
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Install dependencies based on the preferred package manager
COPY package.json package-lock.json* ./
RUN npm ci --only=production

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Build the frontend application
RUN npm run build:client

# Production image with nginx
FROM nginx:alpine AS runner

# Copy built application
COPY --from=builder /app/client/dist /usr/share/nginx/html

# Copy nginx configuration
COPY nginx-frontend.conf /etc/nginx/conf.d/default.conf

# Create nginx user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S buildmaster -u 1001

# Set permissions
RUN chown -R buildmaster:nodejs /usr/share/nginx/html
RUN chown -R buildmaster:nodejs /var/cache/nginx
RUN chown -R buildmaster:nodejs /var/log/nginx
RUN chown -R buildmaster:nodejs /etc/nginx/conf.d
RUN touch /var/run/nginx.pid
RUN chown -R buildmaster:nodejs /var/run/nginx.pid

USER buildmaster

EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:80 || exit 1

CMD ["nginx", "-g", "daemon off;"]
