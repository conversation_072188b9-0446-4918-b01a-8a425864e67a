


Build a personal-use clone of mgx.dev, enhanced with seamless multi-agent collaboration for a no-code AI app builder. The system will support the dynamic integration and coordination of multiple AI personas — including Team Leader, Product Manager, Architect, Engineer, and Data Analyst — based on MetaGPT’s SOPs.

The platform should replicate and extend mgx.dev’s core features:
	•	Structured task decomposition and role-based task assignment
	•	Context-aware collaboration between agents
	•	Project planning and workflow automation
	•	Memory and state persistence across sessions
	•	Modern, intuitive UI with real-time interaction
	•	Support for a no-code/low-code interface to build, connect, and deploy AI workflows or apps using modular components and predefined agent logic

Develop code for architecting, implementing, and optimizing this system using technologies like CrewAI, LangChain, Groq, and SambaNova, while keeping the platform extensible, user-friendly, and tailored for personal use.

Reference - 
https://github.com/geekan/MetaGPT-docs

https://mgx.dev
* https://docs.sambanova.ai/cloud/docs/get-started/overview

* https://docs.sambanova.ai/cloud/docs/integrations/crewAI   

* https://docs.sambanova.ai/cloud/docs/integrations/autogen        

* https://docs.sambanova.ai/cloud/docs/integrations/langchain        

* https://docs.sambanova.ai/cloud/docs/integrations/vercel
*                              
* https://docs.sambanova.ai/cloud/api-reference/endpoints/chat                           

* https://github.com/sambanova/ai-starter-kit                                                      
* 
* https://github.com/sambanova/agents
*                                                                   
* https://github.com/sambanova/integrations
*                                               
* https://github.com/sambanova/langchain-sambanova  

* https://github.com/crewAIInc/crewAI

* https://github.com/crewAIInc/crewAI-tools

* https://github.com/langchain-ai/langchain

Reference code - 
'''
import os
import openai

client = openai.OpenAI(
    api_key=os.environ.get("SAMBANOVA_API_KEY"),
    base_url="https://api.sambanova.ai/v1",
)

response = client.chat.completions.create(
    model="Llama-4-Maverick-17B-128E-Instruct",
    messages=[{"role":"user","content":[{"type":"text","text":"What do you see in this image"},{"type":"image_url","image_url":{"url":"<image_in_base_64>"}}]}],
    temperature=0.1,
    top_p=0.1
)

print(response.choices[0].message.content)