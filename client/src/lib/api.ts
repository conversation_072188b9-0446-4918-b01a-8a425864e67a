import { apiRequest } from "./queryClient";
import type { 
  Project, InsertProject, Agent, InsertAgent, 
  WorkflowNode, InsertWorkflowNode, WorkflowConnection, InsertWorkflowConnection,
  Message, InsertMessage, Task, InsertTask,
  AgentCollaboration, InsertAgentCollaboration,
  AppDeployment, InsertAppDeployment,
  AppAnalytics, InsertAppAnalytics,
  LearningData, InsertLearningData,
  CodeGeneration, InsertCodeGeneration
} from "@shared/schema";

export class ApiClient {
  // Projects
  static async getProjects(): Promise<Project[]> {
    const response = await apiRequest("GET", "/api/projects");
    return response.json();
  }

  static async getProject(id: number): Promise<Project> {
    const response = await apiRequest("GET", `/api/projects/${id}`);
    return response.json();
  }

  static async createProject(data: InsertProject): Promise<Project> {
    const response = await apiRequest("POST", "/api/projects", data);
    return response.json();
  }

  static async updateProject(id: number, data: Partial<Project>): Promise<Project> {
    const response = await apiRequest("PATCH", `/api/projects/${id}`, data);
    return response.json();
  }

  // Agents
  static async getAgents(projectId: number): Promise<Agent[]> {
    const response = await apiRequest("GET", `/api/projects/${projectId}/agents`);
    return response.json();
  }

  static async createAgent(data: InsertAgent): Promise<Agent> {
    const response = await apiRequest("POST", "/api/agents", data);
    return response.json();
  }

  static async updateAgent(id: number, data: Partial<Agent>): Promise<Agent> {
    const response = await apiRequest("PATCH", `/api/agents/${id}`, data);
    return response.json();
  }

  // Workflow
  static async getWorkflowNodes(projectId: number): Promise<WorkflowNode[]> {
    const response = await apiRequest("GET", `/api/projects/${projectId}/workflow/nodes`);
    return response.json();
  }

  static async getWorkflowConnections(projectId: number): Promise<WorkflowConnection[]> {
    const response = await apiRequest("GET", `/api/projects/${projectId}/workflow/connections`);
    return response.json();
  }

  static async createWorkflowNode(data: InsertWorkflowNode): Promise<WorkflowNode> {
    const response = await apiRequest("POST", "/api/workflow/nodes", data);
    return response.json();
  }

  static async updateWorkflowNode(id: number, data: Partial<WorkflowNode>): Promise<WorkflowNode> {
    const response = await apiRequest("PATCH", `/api/workflow/nodes/${id}`, data);
    return response.json();
  }

  static async createWorkflowConnection(data: InsertWorkflowConnection): Promise<WorkflowConnection> {
    const response = await apiRequest("POST", "/api/workflow/connections", data);
    return response.json();
  }

  // Messages
  static async getMessages(projectId: number, limit?: number): Promise<Message[]> {
    const url = limit ? `/api/projects/${projectId}/messages?limit=${limit}` : `/api/projects/${projectId}/messages`;
    const response = await apiRequest("GET", url);
    return response.json();
  }

  static async createMessage(data: InsertMessage): Promise<Message> {
    const response = await apiRequest("POST", "/api/messages", data);
    return response.json();
  }

  // Tasks
  static async getTasks(projectId: number): Promise<Task[]> {
    const response = await apiRequest("GET", `/api/projects/${projectId}/tasks`);
    return response.json();
  }

  static async createTask(data: InsertTask): Promise<Task> {
    const response = await apiRequest("POST", "/api/tasks", data);
    return response.json();
  }

  static async updateTask(id: number, data: Partial<Task>): Promise<Task> {
    const response = await apiRequest("PATCH", `/api/tasks/${id}`, data);
    return response.json();
  }

  // Agent Collaborations
  static async getAgentCollaborations(projectId: number): Promise<AgentCollaboration[]> {
    const response = await apiRequest("GET", `/api/projects/${projectId}/collaborations`);
    return response.json();
  }

  static async createAgentCollaboration(data: InsertAgentCollaboration): Promise<AgentCollaboration> {
    const response = await apiRequest("POST", "/api/collaborations", data);
    return response.json();
  }

  static async updateAgentCollaboration(id: number, data: Partial<AgentCollaboration>): Promise<AgentCollaboration> {
    const response = await apiRequest("PATCH", `/api/collaborations/${id}`, data);
    return response.json();
  }

  // Code Generation
  static async getCodeGenerations(projectId: number): Promise<CodeGeneration[]> {
    const response = await apiRequest("GET", `/api/projects/${projectId}/code-generations`);
    return response.json();
  }

  static async createCodeGeneration(data: InsertCodeGeneration): Promise<CodeGeneration> {
    const response = await apiRequest("POST", "/api/code-generations", data);
    return response.json();
  }

  static async updateCodeGeneration(id: number, data: Partial<CodeGeneration>): Promise<CodeGeneration> {
    const response = await apiRequest("PATCH", `/api/code-generations/${id}`, data);
    return response.json();
  }

  // App Deployments
  static async getAppDeployments(projectId: number): Promise<AppDeployment[]> {
    const response = await apiRequest("GET", `/api/projects/${projectId}/deployments`);
    return response.json();
  }

  static async createAppDeployment(data: InsertAppDeployment): Promise<AppDeployment> {
    const response = await apiRequest("POST", "/api/deployments", data);
    return response.json();
  }

  static async updateAppDeployment(id: number, data: Partial<AppDeployment>): Promise<AppDeployment> {
    const response = await apiRequest("PATCH", `/api/deployments/${id}`, data);
    return response.json();
  }

  // App Analytics
  static async getAppAnalytics(projectId: number, deploymentId?: number): Promise<AppAnalytics[]> {
    const url = deploymentId 
      ? `/api/projects/${projectId}/analytics?deploymentId=${deploymentId}`
      : `/api/projects/${projectId}/analytics`;
    const response = await apiRequest("GET", url);
    return response.json();
  }

  static async createAppAnalytics(data: InsertAppAnalytics): Promise<AppAnalytics> {
    const response = await apiRequest("POST", "/api/analytics", data);
    return response.json();
  }

  // Learning Data
  static async getLearningData(projectId: number, agentId?: number): Promise<LearningData[]> {
    const url = agentId 
      ? `/api/projects/${projectId}/learning-data?agentId=${agentId}`
      : `/api/projects/${projectId}/learning-data`;
    const response = await apiRequest("GET", url);
    return response.json();
  }

  static async createLearningData(data: InsertLearningData): Promise<LearningData> {
    const response = await apiRequest("POST", "/api/learning-data", data);
    return response.json();
  }

  // AI Integration
  static async chatWithAI(message: string, agentId?: number, projectId?: number): Promise<{ userMessage: Message; aiMessage: Message }> {
    const response = await apiRequest("POST", "/api/ai/chat", { message, agentId, projectId });
    return response.json();
  }

  static async runWorkflow(projectId: number): Promise<{ success: boolean; message: string }> {
    const response = await apiRequest("POST", `/api/projects/${projectId}/run`);
    return response.json();
  }
}

export default ApiClient;
