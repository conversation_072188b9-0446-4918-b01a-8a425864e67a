interface SambaNovaConfig {
  apiKey: string;
  baseUrl: string;
  model: string;
}

interface ChatMessage {
  role: "user" | "assistant" | "system";
  content: string;
}

interface ChatResponse {
  choices: Array<{
    message: {
      content: string;
    };
  }>;
}

class SambaNovaClient {
  private config: SambaNovaConfig;

  constructor() {
    this.config = {
      apiKey: import.meta.env.VITE_SAMBANOVA_API_KEY || "",
      baseUrl: "https://api.sambanova.ai/v1",
      model: "Llama-4-Maverick-17B-128E-Instruct"
    };
  }

  async chat(messages: ChatMessage[]): Promise<string> {
    if (!this.config.apiKey) {
      throw new Error("SambaNova API key not configured");
    }

    try {
      const response = await fetch(`${this.config.baseUrl}/chat/completions`, {
        method: "POST",
        headers: {
          "Authorization": `Bearer ${this.config.apiKey}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          model: this.config.model,
          messages,
          temperature: 0.1,
          top_p: 0.1,
        }),
      });

      if (!response.ok) {
        throw new Error(`SambaNova API error: ${response.statusText}`);
      }

      const data: ChatResponse = await response.json();
      return data.choices[0]?.message?.content || "No response generated";
    } catch (error) {
      console.error("SambaNova API error:", error);
      throw error;
    }
  }

  async generateAgentResponse(agentRole: string, context: string, userMessage: string): Promise<string> {
    const systemPrompt = this.getAgentSystemPrompt(agentRole);
    
    const messages: ChatMessage[] = [
      { role: "system", content: systemPrompt },
      { role: "user", content: `Context: ${context}\n\nUser message: ${userMessage}` }
    ];

    return await this.chat(messages);
  }

  private getAgentSystemPrompt(role: string): string {
    const prompts = {
      team_leader: "You are a Team Leader AI agent responsible for coordinating project workflows, assigning tasks, and ensuring team alignment. You excel at strategic planning and team management.",
      product_manager: "You are a Product Manager AI agent focused on analyzing requirements, creating user stories, defining features, and managing product roadmaps. You understand user needs and business objectives.",
      architect: "You are a Software Architect AI agent specializing in system design, technical architecture, technology stack decisions, and scalability planning. You think in terms of high-level system structure.",
      engineer: "You are a Software Engineer AI agent skilled in coding, implementation, testing, and technical problem-solving. You focus on writing clean, efficient code and building robust solutions.",
      data_analyst: "You are a Data Analyst AI agent expert in data analysis, metrics, reporting, and insights generation. You help teams make data-driven decisions."
    };

    return prompts[role as keyof typeof prompts] || "You are a helpful AI assistant.";
  }
}

export const sambaNovaClient = new SambaNovaClient();
