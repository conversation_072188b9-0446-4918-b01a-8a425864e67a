interface SambaNovaConfig {
  apiKey: string;
  baseUrl: string;
  model: string;
}

interface ChatMessage {
  role: "user" | "assistant" | "system";
  content: string;
}

interface ChatResponse {
  choices: Array<{
    message: {
      content: string;
    };
  }>;
}

interface AgentTask {
  id: string;
  description: string;
  agent: string;
  dependencies: string[];
  status: "pending" | "in_progress" | "completed" | "failed";
  result?: string;
}

interface CrewConfig {
  agents: Array<{
    role: string;
    goal: string;
    backstory: string;
    capabilities: string[];
  }>;
  tasks: AgentTask[];
  process: "sequential" | "hierarchical" | "consensus";
}

class SambaNovaClient {
  private config: SambaNovaConfig;

  constructor() {
    this.config = {
      apiKey: import.meta.env.VITE_SAMBANOVA_API_KEY || "",
      baseUrl: "https://api.sambanova.ai/v1",
      model: "Llama-4-Maverick-17B-128E-Instruct"
    };
  }

  async chat(messages: ChatMessage[]): Promise<string> {
    if (!this.config.apiKey) {
      throw new Error("SambaNova API key not configured");
    }

    try {
      const response = await fetch(`${this.config.baseUrl}/chat/completions`, {
        method: "POST",
        headers: {
          "Authorization": `Bearer ${this.config.apiKey}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          model: this.config.model,
          messages,
          temperature: 0.1,
          top_p: 0.1,
        }),
      });

      if (!response.ok) {
        throw new Error(`SambaNova API error: ${response.statusText}`);
      }

      const data: ChatResponse = await response.json();
      return data.choices[0]?.message?.content || "No response generated";
    } catch (error) {
      console.error("SambaNova API error:", error);
      throw error;
    }
  }

  async generateAgentResponse(agentRole: string, context: string, userMessage: string): Promise<string> {
    const systemPrompt = this.getAgentSystemPrompt(agentRole);

    const messages: ChatMessage[] = [
      { role: "system", content: systemPrompt },
      { role: "user", content: `Context: ${context}\n\nUser message: ${userMessage}` }
    ];

    return await this.chat(messages);
  }

  private getAgentSystemPrompt(role: string): string {
    const prompts = {
      team_leader: "You are a Team Leader AI agent responsible for coordinating project workflows, assigning tasks, and ensuring team alignment. You excel at strategic planning and team management. You can negotiate conflicts, allocate resources, and make executive decisions.",
      product_manager: "You are a Product Manager AI agent focused on analyzing requirements, creating user stories, defining features, and managing product roadmaps. You understand user needs and business objectives. You can clarify ambiguous requirements and prioritize features.",
      architect: "You are a Software Architect AI agent specializing in system design, technical architecture, technology stack decisions, and scalability planning. You think in terms of high-level system structure. You can design databases, APIs, and system integrations.",
      engineer: "You are a Software Engineer AI agent skilled in coding, implementation, testing, and technical problem-solving. You focus on writing clean, efficient code and building robust solutions. You can generate code, fix bugs, and optimize performance.",
      data_analyst: "You are a Data Analyst AI agent expert in data analysis, metrics, reporting, and insights generation. You help teams make data-driven decisions. You can analyze performance data and predict issues."
    };

    return prompts[role as keyof typeof prompts] || "You are a helpful AI assistant.";
  }

  // CrewAI Integration
  async createCrew(config: CrewConfig): Promise<string> {
    const crewPrompt = `
Create a multi-agent crew with the following configuration:
Agents: ${JSON.stringify(config.agents, null, 2)}
Tasks: ${JSON.stringify(config.tasks, null, 2)}
Process: ${config.process}

Coordinate the agents to work together on these tasks, ensuring proper communication and task dependencies.
`;

    const messages: ChatMessage[] = [
      { role: "system", content: "You are a CrewAI coordinator responsible for managing multi-agent workflows." },
      { role: "user", content: crewPrompt }
    ];

    return await this.chat(messages);
  }

  async executeCrewTask(taskId: string, context: string): Promise<string> {
    const messages: ChatMessage[] = [
      { role: "system", content: "You are executing a task as part of a multi-agent crew. Coordinate with other agents as needed." },
      { role: "user", content: `Execute task ${taskId} with context: ${context}` }
    ];

    return await this.chat(messages);
  }

  // LangChain Integration
  async createLangChain(steps: Array<{ type: string; prompt: string; agent?: string }>): Promise<string> {
    const chainPrompt = `
Execute the following LangChain workflow:
${steps.map((step, i) => `Step ${i + 1} (${step.type}): ${step.prompt} ${step.agent ? `[Agent: ${step.agent}]` : ''}`).join('\n')}

Process each step sequentially, using the output of previous steps as input for subsequent steps.
`;

    const messages: ChatMessage[] = [
      { role: "system", content: "You are a LangChain executor responsible for processing sequential AI workflows." },
      { role: "user", content: chainPrompt }
    ];

    return await this.chat(messages);
  }

  // Advanced Code Generation
  async generateFullStackCode(requirements: string, techStack: string[]): Promise<{
    frontend: string;
    backend: string;
    database: string;
    deployment: string;
  }> {
    const codePrompt = `
Generate a complete full-stack application based on these requirements:
${requirements}

Tech Stack: ${techStack.join(', ')}

Provide:
1. Frontend code (React/TypeScript)
2. Backend code (Node.js/Express)
3. Database schema (PostgreSQL)
4. Deployment configuration (Docker/Vercel)

Make the code production-ready with proper error handling, validation, and security.
`;

    const response = await this.chat([
      { role: "system", content: "You are an expert full-stack developer capable of generating complete, production-ready applications." },
      { role: "user", content: codePrompt }
    ]);

    // Parse the response to extract different code sections
    const sections = this.parseCodeSections(response);
    return {
      frontend: sections.frontend || "",
      backend: sections.backend || "",
      database: sections.database || "",
      deployment: sections.deployment || ""
    };
  }

  private parseCodeSections(response: string): Record<string, string> {
    const sections: Record<string, string> = {};
    const sectionRegex = /```(\w+)?\n([\s\S]*?)```/g;
    let match;

    while ((match = sectionRegex.exec(response)) !== null) {
      const language = match[1] || 'unknown';
      const code = match[2];

      if (code.includes('React') || code.includes('tsx') || code.includes('jsx')) {
        sections.frontend = code;
      } else if (code.includes('express') || code.includes('app.')) {
        sections.backend = code;
      } else if (code.includes('CREATE TABLE') || code.includes('schema')) {
        sections.database = code;
      } else if (code.includes('Dockerfile') || code.includes('vercel')) {
        sections.deployment = code;
      }
    }

    return sections;
  }

  // Self-Healing and Optimization
  async analyzeBugReport(error: string, code: string, context: string): Promise<{
    diagnosis: string;
    fix: string;
    prevention: string;
  }> {
    const bugPrompt = `
Analyze this bug report:
Error: ${error}
Code: ${code}
Context: ${context}

Provide:
1. Diagnosis of the root cause
2. Specific fix for the issue
3. Prevention strategies for similar issues
`;

    const response = await this.chat([
      { role: "system", content: "You are an expert debugging AI that can analyze and fix code issues." },
      { role: "user", content: bugPrompt }
    ]);

    return this.parseBugAnalysis(response);
  }

  private parseBugAnalysis(response: string): { diagnosis: string; fix: string; prevention: string } {
    const sections = response.split(/\d+\./);
    return {
      diagnosis: sections[1]?.trim() || "",
      fix: sections[2]?.trim() || "",
      prevention: sections[3]?.trim() || ""
    };
  }

  async optimizeCode(code: string, metrics: any): Promise<{
    optimizedCode: string;
    improvements: string[];
    performanceGains: string;
  }> {
    const optimizationPrompt = `
Optimize this code for better performance:
${code}

Current metrics: ${JSON.stringify(metrics)}

Provide optimized code with explanations of improvements and expected performance gains.
`;

    const response = await this.chat([
      { role: "system", content: "You are a performance optimization expert specializing in code efficiency and scalability." },
      { role: "user", content: optimizationPrompt }
    ]);

    return this.parseOptimizationResponse(response);
  }

  private parseOptimizationResponse(response: string): {
    optimizedCode: string;
    improvements: string[];
    performanceGains: string;
  } {
    const codeMatch = response.match(/```[\s\S]*?```/);
    const optimizedCode = codeMatch ? codeMatch[0].replace(/```\w*\n?/g, '').trim() : "";

    const improvements = response.match(/- (.*)/g)?.map(item => item.replace('- ', '')) || [];
    const performanceSection = response.match(/performance.*?:/i);
    const performanceGains = performanceSection ? response.substring(performanceSection.index!) : "";

    return {
      optimizedCode,
      improvements,
      performanceGains
    };
  }
}

export const sambaNovaClient = new SambaNovaClient();
