import React, { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown, 
  AlertTriangle, 
  CheckCircle,
  Activity,
  Users,
  Clock,
  Zap,
  RefreshCw
} from "lucide-react";
import ApiClient from "@/lib/api";

interface AnalyticsDashboardProps {
  projectId: number;
  deploymentId?: number;
}

interface MetricCard {
  title: string;
  value: string | number;
  change?: number;
  icon: React.ReactNode;
  color: string;
}

export function AnalyticsDashboard({ projectId, deploymentId }: AnalyticsDashboardProps) {
  const [selectedTimeRange, setSelectedTimeRange] = useState("24h");
  const [realTimeData, setRealTimeData] = useState<any>(null);

  const { data: analytics, refetch } = useQuery({
    queryKey: ["analytics", projectId, deploymentId],
    queryFn: () => ApiClient.getAppAnalytics(projectId, deploymentId),
    refetchInterval: 30000, // Refresh every 30 seconds
  });

  const { data: deployments } = useQuery({
    queryKey: ["deployments", projectId],
    queryFn: () => ApiClient.getAppDeployments(projectId),
  });

  const { data: predictions } = useQuery({
    queryKey: ["predictions", projectId],
    queryFn: () => ApiClient.predictIssues(projectId),
  });

  useEffect(() => {
    // Simulate real-time data updates
    const interval = setInterval(() => {
      setRealTimeData({
        activeUsers: Math.floor(Math.random() * 100),
        requestsPerSecond: Math.floor(Math.random() * 50),
        responseTime: Math.floor(Math.random() * 200) + 100,
        errorRate: Math.random() * 0.05,
        timestamp: new Date(),
      });
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const metricCards: MetricCard[] = [
    {
      title: "Active Users",
      value: realTimeData?.activeUsers || 0,
      change: 12,
      icon: <Users className="h-4 w-4" />,
      color: "text-blue-600",
    },
    {
      title: "Response Time",
      value: `${realTimeData?.responseTime || 150}ms`,
      change: -5,
      icon: <Clock className="h-4 w-4" />,
      color: "text-green-600",
    },
    {
      title: "Requests/sec",
      value: realTimeData?.requestsPerSecond || 25,
      change: 8,
      icon: <Activity className="h-4 w-4" />,
      color: "text-purple-600",
    },
    {
      title: "Error Rate",
      value: `${((realTimeData?.errorRate || 0) * 100).toFixed(2)}%`,
      change: -2,
      icon: <AlertTriangle className="h-4 w-4" />,
      color: "text-red-600",
    },
  ];

  const getChangeIcon = (change: number) => {
    if (change > 0) return <TrendingUp className="h-3 w-3 text-green-500" />;
    if (change < 0) return <TrendingDown className="h-3 w-3 text-red-500" />;
    return null;
  };

  const getAlertSeverityColor = (severity: string) => {
    switch (severity) {
      case "critical": return "bg-red-100 text-red-800";
      case "high": return "bg-orange-100 text-orange-800";
      case "medium": return "bg-yellow-100 text-yellow-800";
      case "low": return "bg-blue-100 text-blue-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Analytics Dashboard</h2>
          <p className="text-gray-600">Real-time monitoring and insights</p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={() => refetch()}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <select
            value={selectedTimeRange}
            onChange={(e) => setSelectedTimeRange(e.target.value)}
            className="px-3 py-1 border rounded-md text-sm"
          >
            <option value="1h">Last Hour</option>
            <option value="24h">Last 24 Hours</option>
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
          </select>
        </div>
      </div>

      {/* Real-time Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {metricCards.map((metric, index) => (
          <Card key={index}>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className={metric.color}>
                  {metric.icon}
                </div>
                {metric.change !== undefined && (
                  <div className="flex items-center gap-1">
                    {getChangeIcon(metric.change)}
                    <span className="text-xs text-gray-600">
                      {Math.abs(metric.change)}%
                    </span>
                  </div>
                )}
              </div>
              <div className="mt-2">
                <div className="text-2xl font-bold">{metric.value}</div>
                <div className="text-sm text-gray-600">{metric.title}</div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Main Analytics Tabs */}
      <Card>
        <CardContent className="p-0">
          <Tabs defaultValue="overview" className="w-full">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="performance">Performance</TabsTrigger>
              <TabsTrigger value="alerts">Alerts</TabsTrigger>
              <TabsTrigger value="predictions">Predictions</TabsTrigger>
              <TabsTrigger value="insights">Insights</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="p-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Traffic Chart Placeholder */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <BarChart3 className="h-5 w-5" />
                      Traffic Overview
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
                      <div className="text-center text-gray-500">
                        <BarChart3 className="h-12 w-12 mx-auto mb-2 opacity-50" />
                        <p>Traffic chart would be displayed here</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* System Health */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Activity className="h-5 w-5" />
                      System Health
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>CPU Usage</span>
                        <span>45%</span>
                      </div>
                      <Progress value={45} className="h-2" />
                    </div>
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>Memory Usage</span>
                        <span>62%</span>
                      </div>
                      <Progress value={62} className="h-2" />
                    </div>
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>Disk Usage</span>
                        <span>38%</span>
                      </div>
                      <Progress value={38} className="h-2" />
                    </div>
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>Network I/O</span>
                        <span>23%</span>
                      </div>
                      <Progress value={23} className="h-2" />
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="performance" className="p-6">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Response Time</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-3xl font-bold mb-2">
                      {realTimeData?.responseTime || 150}ms
                    </div>
                    <div className="text-sm text-gray-600">Average response time</div>
                    <div className="mt-4 h-32 bg-gray-50 rounded-lg flex items-center justify-center">
                      <span className="text-gray-500">Response time chart</span>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Throughput</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-3xl font-bold mb-2">
                      {realTimeData?.requestsPerSecond || 25}/s
                    </div>
                    <div className="text-sm text-gray-600">Requests per second</div>
                    <div className="mt-4 h-32 bg-gray-50 rounded-lg flex items-center justify-center">
                      <span className="text-gray-500">Throughput chart</span>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Error Rate</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-3xl font-bold mb-2">
                      {((realTimeData?.errorRate || 0) * 100).toFixed(2)}%
                    </div>
                    <div className="text-sm text-gray-600">Error percentage</div>
                    <div className="mt-4 h-32 bg-gray-50 rounded-lg flex items-center justify-center">
                      <span className="text-gray-500">Error rate chart</span>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="alerts" className="p-6">
              <div className="space-y-4">
                {/* Mock alerts */}
                {[
                  {
                    id: 1,
                    type: "performance",
                    severity: "medium",
                    message: "Response time increased by 15% in the last hour",
                    timestamp: new Date(Date.now() - 30 * 60 * 1000),
                    resolved: false,
                  },
                  {
                    id: 2,
                    type: "error",
                    severity: "low",
                    message: "Minor increase in 404 errors detected",
                    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
                    resolved: true,
                  },
                ].map((alert) => (
                  <Card key={alert.id}>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          {alert.resolved ? (
                            <CheckCircle className="h-5 w-5 text-green-500" />
                          ) : (
                            <AlertTriangle className="h-5 w-5 text-orange-500" />
                          )}
                          <div>
                            <div className="font-medium">{alert.message}</div>
                            <div className="text-sm text-gray-600">
                              {alert.timestamp.toLocaleString()}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge className={getAlertSeverityColor(alert.severity)}>
                            {alert.severity}
                          </Badge>
                          <Badge variant="outline">{alert.type}</Badge>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="predictions" className="p-6">
              <div className="space-y-4">
                {predictions?.predictions?.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <Zap className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No predictions available</p>
                    <p className="text-sm">AI is analyzing your data to provide insights</p>
                  </div>
                ) : (
                  predictions?.predictions?.map((prediction, index) => (
                    <Card key={index}>
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between mb-2">
                          <div className="font-medium">{prediction.description}</div>
                          <Badge variant="outline">
                            {Math.round(prediction.probability)}% confidence
                          </Badge>
                        </div>
                        <div className="text-sm text-gray-600 mb-2">
                          Expected timeframe: {prediction.timeframe}
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge 
                            className={
                              prediction.impact === "high" 
                                ? "bg-red-100 text-red-800"
                                : prediction.impact === "medium"
                                ? "bg-yellow-100 text-yellow-800"
                                : "bg-green-100 text-green-800"
                            }
                          >
                            {prediction.impact} impact
                          </Badge>
                          <Badge variant="outline">{prediction.type}</Badge>
                        </div>
                      </CardContent>
                    </Card>
                  ))
                )}
              </div>
            </TabsContent>

            <TabsContent value="insights" className="p-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Performance Insights</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span className="text-sm">Response times are within acceptable range</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <AlertTriangle className="h-4 w-4 text-yellow-500" />
                      <span className="text-sm">Consider implementing caching for better performance</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span className="text-sm">Error rates are below threshold</span>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Optimization Suggestions</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="text-sm">
                      • Enable gzip compression to reduce bandwidth usage
                    </div>
                    <div className="text-sm">
                      • Implement database query optimization
                    </div>
                    <div className="text-sm">
                      • Consider adding a CDN for static assets
                    </div>
                    <div className="text-sm">
                      • Set up automated scaling based on traffic patterns
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
