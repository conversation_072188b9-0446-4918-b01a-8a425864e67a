import React, { useState } from "react";
import { useMutation, useQuery } from "@tanstack/react-query";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { AlertCircle, CheckCircle, Code, Download, Play, RefreshCw } from "lucide-react";
import ApiClient from "@/lib/api";
import type { CodeGeneration } from "@shared/schema";

interface CodeGenerationProps {
  projectId: number;
  agentId: number;
}

export function CodeGenerationComponent({ projectId, agentId }: CodeGenerationProps) {
  const [prompt, setPrompt] = useState("");
  const [codeType, setCodeType] = useState("component");
  const [language, setLanguage] = useState("typescript");
  const [selectedGeneration, setSelectedGeneration] = useState<CodeGeneration | null>(null);

  const { data: codeGenerations, refetch } = useQuery({
    queryKey: ["code-generations", projectId],
    queryFn: () => ApiClient.getCodeGenerations(projectId),
  });

  const generateCodeMutation = useMutation({
    mutationFn: (data: { prompt: string; type: string; language: string }) =>
      ApiClient.generateCode(data.prompt, data.type, data.language, projectId, agentId),
    onSuccess: () => {
      refetch();
      setPrompt("");
    },
  });

  const handleGenerate = () => {
    if (!prompt.trim()) return;
    generateCodeMutation.mutate({ prompt, type: codeType, language });
  };

  const getQualityColor = (quality: number) => {
    if (quality >= 8) return "bg-green-500";
    if (quality >= 6) return "bg-yellow-500";
    return "bg-red-500";
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "generated": return "bg-green-100 text-green-800";
      case "generating": return "bg-blue-100 text-blue-800";
      case "failed": return "bg-red-100 text-red-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="space-y-6">
      {/* Code Generation Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Code className="h-5 w-5" />
            AI Code Generation
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium">Code Type</label>
              <Select value={codeType} onValueChange={setCodeType}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="component">React Component</SelectItem>
                  <SelectItem value="function">Function</SelectItem>
                  <SelectItem value="class">Class</SelectItem>
                  <SelectItem value="api">API Endpoint</SelectItem>
                  <SelectItem value="database">Database Schema</SelectItem>
                  <SelectItem value="test">Test Suite</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-sm font-medium">Language</label>
              <Select value={language} onValueChange={setLanguage}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="typescript">TypeScript</SelectItem>
                  <SelectItem value="javascript">JavaScript</SelectItem>
                  <SelectItem value="python">Python</SelectItem>
                  <SelectItem value="java">Java</SelectItem>
                  <SelectItem value="sql">SQL</SelectItem>
                  <SelectItem value="css">CSS</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <div>
            <label className="text-sm font-medium">Prompt</label>
            <Textarea
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              placeholder="Describe what code you want to generate..."
              rows={4}
            />
          </div>

          <Button 
            onClick={handleGenerate}
            disabled={!prompt.trim() || generateCodeMutation.isPending}
            className="w-full"
          >
            {generateCodeMutation.isPending ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Generating...
              </>
            ) : (
              <>
                <Play className="h-4 w-4 mr-2" />
                Generate Code
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {/* Generated Code List */}
      <Card>
        <CardHeader>
          <CardTitle>Generated Code</CardTitle>
        </CardHeader>
        <CardContent>
          {!codeGenerations?.length ? (
            <div className="text-center py-8 text-gray-500">
              No code generated yet. Create your first generation above!
            </div>
          ) : (
            <div className="space-y-4">
              {codeGenerations.map((generation) => (
                <div
                  key={generation.id}
                  className="border rounded-lg p-4 cursor-pointer hover:bg-gray-50"
                  onClick={() => setSelectedGeneration(generation)}
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">{generation.type}</Badge>
                      <Badge variant="outline">{generation.language}</Badge>
                      <Badge className={getStatusColor(generation.status)}>
                        {generation.status}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="flex items-center gap-1">
                        <div className={`w-2 h-2 rounded-full ${getQualityColor(generation.quality)}`} />
                        <span className="text-sm text-gray-600">Quality: {generation.quality}/10</span>
                      </div>
                      <Button size="sm" variant="outline">
                        <Download className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                  <p className="text-sm text-gray-600 mb-2">{generation.prompt}</p>
                  <div className="text-xs text-gray-500">
                    Generated {new Date(generation.createdAt).toLocaleString()}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Code Preview Modal */}
      {selectedGeneration && (
        <Card className="fixed inset-4 z-50 bg-white shadow-2xl overflow-hidden">
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Code className="h-5 w-5" />
              {selectedGeneration.type} - {selectedGeneration.language}
            </CardTitle>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setSelectedGeneration(null)}
            >
              Close
            </Button>
          </CardHeader>
          <CardContent className="h-full overflow-auto">
            <Tabs defaultValue="code" className="h-full">
              <TabsList>
                <TabsTrigger value="code">Generated Code</TabsTrigger>
                <TabsTrigger value="details">Details</TabsTrigger>
                <TabsTrigger value="metrics">Metrics</TabsTrigger>
              </TabsList>
              
              <TabsContent value="code" className="h-full">
                <div className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-auto h-96">
                  <pre className="text-sm">
                    <code>{selectedGeneration.generatedCode}</code>
                  </pre>
                </div>
              </TabsContent>
              
              <TabsContent value="details" className="space-y-4">
                <div>
                  <h4 className="font-medium mb-2">Prompt</h4>
                  <p className="text-sm text-gray-600 bg-gray-50 p-3 rounded">
                    {selectedGeneration.prompt}
                  </p>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-medium mb-2">Quality Score</h4>
                    <div className="flex items-center gap-2">
                      <Progress value={selectedGeneration.quality * 10} className="flex-1" />
                      <span className="text-sm font-medium">{selectedGeneration.quality}/10</span>
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="font-medium mb-2">Status</h4>
                    <Badge className={getStatusColor(selectedGeneration.status)}>
                      {selectedGeneration.status}
                    </Badge>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium mb-2">Generated</h4>
                  <p className="text-sm text-gray-600">
                    {new Date(selectedGeneration.createdAt).toLocaleString()}
                  </p>
                </div>
              </TabsContent>
              
              <TabsContent value="metrics" className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <Card>
                    <CardContent className="p-4">
                      <div className="text-2xl font-bold">{selectedGeneration.generatedCode.split('\n').length}</div>
                      <div className="text-sm text-gray-600">Lines of Code</div>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardContent className="p-4">
                      <div className="text-2xl font-bold">{selectedGeneration.generatedCode.length}</div>
                      <div className="text-sm text-gray-600">Characters</div>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardContent className="p-4">
                      <div className="text-2xl font-bold">{selectedGeneration.generatedCode.split(/\s+/).length}</div>
                      <div className="text-sm text-gray-600">Words</div>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardContent className="p-4">
                      <div className="text-2xl font-bold">{selectedGeneration.quality}/10</div>
                      <div className="text-sm text-gray-600">Quality Score</div>
                    </CardContent>
                  </Card>
                </div>

                <div>
                  <h4 className="font-medium mb-2">Code Analysis</h4>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span className="text-sm">Syntax validation passed</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span className="text-sm">Best practices followed</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <AlertCircle className="h-4 w-4 text-yellow-500" />
                      <span className="text-sm">Consider adding error handling</span>
                    </div>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
