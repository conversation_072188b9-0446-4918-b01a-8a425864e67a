import React, { useState } from "react";
import { useMutation, useQuery } from "@tanstack/react-query";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { Progress } from "@/components/ui/progress";
import { 
  Users, 
  MessageSquare, 
  AlertTriangle, 
  CheckCircle, 
  Clock,
  Handshake,
  Brain,
  Target,
  TrendingUp,
  Settings
} from "lucide-react";
import ApiClient from "@/lib/api";
import type { AgentCollaboration, Agent } from "@shared/schema";

interface AgentCollaborationProps {
  projectId: number;
}

interface CollaborationMetrics {
  totalCollaborations: number;
  activeCollaborations: number;
  resolvedConflicts: number;
  successRate: number;
}

export function AgentCollaborationComponent({ projectId }: AgentCollaborationProps) {
  const [selectedCollaboration, setSelectedCollaboration] = useState<AgentCollaboration | null>(null);
  const [resolutionText, setResolutionText] = useState("");

  const { data: collaborations, refetch } = useQuery({
    queryKey: ["collaborations", projectId],
    queryFn: () => ApiClient.getAgentCollaborations(projectId),
  });

  const { data: agents } = useQuery({
    queryKey: ["agents", projectId],
    queryFn: () => ApiClient.getAgents(projectId),
  });

  const negotiateConflictMutation = useMutation({
    mutationFn: (data: { collaborationId: number; resolution: string }) =>
      ApiClient.negotiateAgentConflict(data.collaborationId, data.resolution),
    onSuccess: () => {
      refetch();
      setSelectedCollaboration(null);
      setResolutionText("");
    },
  });

  const getAgentName = (agentId: number) => {
    const agent = agents?.find(a => a.id === agentId);
    return agent ? `${agent.role} Agent` : `Agent ${agentId}`;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active": return "bg-blue-100 text-blue-800";
      case "resolved": return "bg-green-100 text-green-800";
      case "conflict": return "bg-red-100 text-red-800";
      case "negotiating": return "bg-yellow-100 text-yellow-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const getCollaborationTypeIcon = (type: string) => {
    switch (type) {
      case "task_assignment": return <Target className="h-4 w-4" />;
      case "knowledge_sharing": return <Brain className="h-4 w-4" />;
      case "conflict_resolution": return <Handshake className="h-4 w-4" />;
      case "performance_review": return <TrendingUp className="h-4 w-4" />;
      default: return <Users className="h-4 w-4" />;
    }
  };

  const calculateMetrics = (): CollaborationMetrics => {
    if (!collaborations) {
      return {
        totalCollaborations: 0,
        activeCollaborations: 0,
        resolvedConflicts: 0,
        successRate: 0,
      };
    }

    const total = collaborations.length;
    const active = collaborations.filter(c => c.status === "active").length;
    const resolved = collaborations.filter(c => c.status === "resolved").length;
    const successRate = total > 0 ? (resolved / total) * 100 : 0;

    return {
      totalCollaborations: total,
      activeCollaborations: active,
      resolvedConflicts: resolved,
      successRate,
    };
  };

  const metrics = calculateMetrics();

  const handleNegotiateConflict = (collaboration: AgentCollaboration) => {
    setSelectedCollaboration(collaboration);
  };

  const handleSubmitResolution = () => {
    if (!selectedCollaboration || !resolutionText.trim()) return;
    
    negotiateConflictMutation.mutate({
      collaborationId: selectedCollaboration.id,
      resolution: resolutionText,
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Agent Collaboration</h2>
          <p className="text-gray-600">Monitor and manage agent interactions</p>
        </div>
      </div>

      {/* Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <Users className="h-5 w-5 text-blue-600" />
              <Badge variant="outline">{metrics.totalCollaborations}</Badge>
            </div>
            <div className="mt-2">
              <div className="text-2xl font-bold">{metrics.totalCollaborations}</div>
              <div className="text-sm text-gray-600">Total Collaborations</div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <Clock className="h-5 w-5 text-orange-600" />
              <Badge variant="outline">{metrics.activeCollaborations}</Badge>
            </div>
            <div className="mt-2">
              <div className="text-2xl font-bold">{metrics.activeCollaborations}</div>
              <div className="text-sm text-gray-600">Active Sessions</div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <Handshake className="h-5 w-5 text-green-600" />
              <Badge variant="outline">{metrics.resolvedConflicts}</Badge>
            </div>
            <div className="mt-2">
              <div className="text-2xl font-bold">{metrics.resolvedConflicts}</div>
              <div className="text-sm text-gray-600">Resolved Conflicts</div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <TrendingUp className="h-5 w-5 text-purple-600" />
              <Badge variant="outline">{Math.round(metrics.successRate)}%</Badge>
            </div>
            <div className="mt-2">
              <div className="text-2xl font-bold">{Math.round(metrics.successRate)}%</div>
              <div className="text-sm text-gray-600">Success Rate</div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Card>
        <CardContent className="p-0">
          <Tabs defaultValue="active" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="active">Active Sessions</TabsTrigger>
              <TabsTrigger value="conflicts">Conflicts</TabsTrigger>
              <TabsTrigger value="history">History</TabsTrigger>
              <TabsTrigger value="insights">Insights</TabsTrigger>
            </TabsList>

            <TabsContent value="active" className="p-6">
              <div className="space-y-4">
                {collaborations?.filter(c => c.status === "active").length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No active collaborations</p>
                    <p className="text-sm">Agents will appear here when they start collaborating</p>
                  </div>
                ) : (
                  collaborations?.filter(c => c.status === "active").map((collaboration) => (
                    <Card key={collaboration.id}>
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center gap-2">
                            {getCollaborationTypeIcon(collaboration.type)}
                            <span className="font-medium">{collaboration.type.replace('_', ' ')}</span>
                          </div>
                          <Badge className={getStatusColor(collaboration.status)}>
                            {collaboration.status}
                          </Badge>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-4 mb-3">
                          <div>
                            <div className="text-sm text-gray-600">Primary Agent</div>
                            <div className="font-medium">{getAgentName(collaboration.primaryAgentId)}</div>
                          </div>
                          <div>
                            <div className="text-sm text-gray-600">Secondary Agent</div>
                            <div className="font-medium">{getAgentName(collaboration.secondaryAgentId)}</div>
                          </div>
                        </div>

                        <div className="text-sm text-gray-600 mb-3">
                          Started {new Date(collaboration.createdAt).toLocaleString()}
                        </div>

                        {collaboration.data && (
                          <div className="bg-gray-50 p-3 rounded-lg">
                            <div className="text-sm">
                              <strong>Context:</strong> {JSON.stringify(collaboration.data, null, 2)}
                            </div>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  ))
                )}
              </div>
            </TabsContent>

            <TabsContent value="conflicts" className="p-6">
              <div className="space-y-4">
                {collaborations?.filter(c => c.status === "conflict").length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <CheckCircle className="h-12 w-12 mx-auto mb-4 opacity-50 text-green-500" />
                    <p>No active conflicts</p>
                    <p className="text-sm">All agents are working harmoniously</p>
                  </div>
                ) : (
                  collaborations?.filter(c => c.status === "conflict").map((collaboration) => (
                    <Card key={collaboration.id} className="border-red-200">
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center gap-2">
                            <AlertTriangle className="h-5 w-5 text-red-500" />
                            <span className="font-medium text-red-700">Conflict Detected</span>
                          </div>
                          <Button
                            size="sm"
                            onClick={() => handleNegotiateConflict(collaboration)}
                          >
                            Negotiate
                          </Button>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-4 mb-3">
                          <div>
                            <div className="text-sm text-gray-600">Agent A</div>
                            <div className="font-medium">{getAgentName(collaboration.primaryAgentId)}</div>
                          </div>
                          <div>
                            <div className="text-sm text-gray-600">Agent B</div>
                            <div className="font-medium">{getAgentName(collaboration.secondaryAgentId)}</div>
                          </div>
                        </div>

                        {collaboration.data && (
                          <div className="bg-red-50 p-3 rounded-lg">
                            <div className="text-sm">
                              <strong>Conflict Details:</strong> {JSON.stringify(collaboration.data, null, 2)}
                            </div>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  ))
                )}
              </div>
            </TabsContent>

            <TabsContent value="history" className="p-6">
              <div className="space-y-4">
                {collaborations?.filter(c => c.status === "resolved").map((collaboration) => (
                  <Card key={collaboration.id}>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center gap-2">
                          {getCollaborationTypeIcon(collaboration.type)}
                          <span className="font-medium">{collaboration.type.replace('_', ' ')}</span>
                        </div>
                        <Badge className={getStatusColor(collaboration.status)}>
                          {collaboration.status}
                        </Badge>
                      </div>
                      
                      <div className="grid grid-cols-3 gap-4 mb-3">
                        <div>
                          <div className="text-sm text-gray-600">Primary Agent</div>
                          <div className="font-medium">{getAgentName(collaboration.primaryAgentId)}</div>
                        </div>
                        <div>
                          <div className="text-sm text-gray-600">Secondary Agent</div>
                          <div className="font-medium">{getAgentName(collaboration.secondaryAgentId)}</div>
                        </div>
                        <div>
                          <div className="text-sm text-gray-600">Duration</div>
                          <div className="font-medium">
                            {collaboration.resolvedAt 
                              ? Math.round((new Date(collaboration.resolvedAt).getTime() - new Date(collaboration.createdAt).getTime()) / (1000 * 60))
                              : 0} min
                          </div>
                        </div>
                      </div>

                      <div className="text-sm text-gray-600">
                        Resolved {collaboration.resolvedAt ? new Date(collaboration.resolvedAt).toLocaleString() : 'N/A'}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="insights" className="p-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Collaboration Patterns</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>Task Assignment</span>
                        <span>65%</span>
                      </div>
                      <Progress value={65} className="h-2" />
                    </div>
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>Knowledge Sharing</span>
                        <span>25%</span>
                      </div>
                      <Progress value={25} className="h-2" />
                    </div>
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>Conflict Resolution</span>
                        <span>10%</span>
                      </div>
                      <Progress value={10} className="h-2" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Performance Metrics</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Average Resolution Time</span>
                      <span className="font-medium">12 minutes</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Conflict Rate</span>
                      <span className="font-medium">5%</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Success Rate</span>
                      <span className="font-medium">{Math.round(metrics.successRate)}%</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Agent Satisfaction</span>
                      <span className="font-medium">8.5/10</span>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Conflict Resolution Modal */}
      {selectedCollaboration && (
        <Card className="fixed inset-4 z-50 bg-white shadow-2xl">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Handshake className="h-5 w-5" />
              Negotiate Conflict Resolution
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <div className="text-sm text-gray-600">Agent A</div>
                <div className="font-medium">{getAgentName(selectedCollaboration.primaryAgentId)}</div>
              </div>
              <div>
                <div className="text-sm text-gray-600">Agent B</div>
                <div className="font-medium">{getAgentName(selectedCollaboration.secondaryAgentId)}</div>
              </div>
            </div>

            <div>
              <label className="text-sm font-medium">Resolution Strategy</label>
              <Textarea
                value={resolutionText}
                onChange={(e) => setResolutionText(e.target.value)}
                placeholder="Describe how this conflict should be resolved..."
                rows={4}
              />
            </div>

            <div className="flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => setSelectedCollaboration(null)}
              >
                Cancel
              </Button>
              <Button
                onClick={handleSubmitResolution}
                disabled={!resolutionText.trim() || negotiateConflictMutation.isPending}
              >
                {negotiateConflictMutation.isPending ? "Negotiating..." : "Apply Resolution"}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
