import React, { useState, useRef, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Card } from "@/components/ui/card";
import { Send, Paperclip, MoreHorizontal } from "lucide-react";
import { Crown, ClipboardCheck, Compass, Code, BarChart } from "lucide-react";
import type { Agent, Message } from "@shared/schema";
import { apiRequest } from "@/lib/queryClient";
import { sambaNovaClient } from "@/lib/sambanova";

interface ChatInterfaceProps {
  projectId: number;
  selectedAgent: Agent | null;
}

const agentIcons = {
  team_leader: Crown,
  product_manager: ClipboardCheck,
  architect: Compass,
  engineer: Code,
  data_analyst: <PERSON><PERSON><PERSON>,
};

const agentColors = {
  team_leader: "bg-emerald-500",
  product_manager: "bg-blue-500",
  architect: "bg-violet-500",
  engineer: "bg-orange-500",
  data_analyst: "bg-teal-500",
};

export function ChatInterface({ projectId, selectedAgent }: ChatInterfaceProps) {
  const [messageInput, setMessageInput] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const queryClient = useQueryClient();

  const { data: messages = [] } = useQuery<Message[]>({
    queryKey: [`/api/projects/${projectId}/messages`],
    refetchInterval: 2000,
  });

  const sendMessageMutation = useMutation({
    mutationFn: async (content: string) => {
      const userMessage = await apiRequest("POST", "/api/messages", {
        projectId,
        agentId: null,
        content,
        type: "message"
      });

      if (selectedAgent) {
        try {
          setIsTyping(true);
          const context = `Project: E-Commerce Platform\nAgent Role: ${selectedAgent.role}\nRecent messages: ${messages.slice(-3).map(m => m.content).join('\n')}`;
          const aiResponse = await sambaNovaClient.generateAgentResponse(selectedAgent.role, context, content);
          
          await apiRequest("POST", "/api/messages", {
            projectId,
            agentId: selectedAgent.id,
            content: aiResponse,
            type: "message"
          });
        } catch (error) {
          console.error("Failed to generate AI response:", error);
          await apiRequest("POST", "/api/messages", {
            projectId,
            agentId: selectedAgent.id,
            content: `As ${selectedAgent.name}, I understand your message: "${content}". I'll help you with this task based on my role as ${selectedAgent.role.replace('_', ' ')}.`,
            type: "message"
          });
        } finally {
          setIsTyping(false);
        }
      }

      return userMessage.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/projects/${projectId}/messages`] });
      setMessageInput("");
    },
  });

  const handleSendMessage = () => {
    if (messageInput.trim()) {
      sendMessageMutation.mutate(messageInput);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  const filteredMessages = selectedAgent 
    ? messages.filter(message => !message.agentId || message.agentId === selectedAgent.id)
    : messages.slice(-10);

  return (
    <div className="flex flex-col h-full">
      {/* Chat Header */}
      {selectedAgent && (
        <div className="border-b border-border px-6 py-4 bg-muted/30">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white ${
                agentColors[selectedAgent.role as keyof typeof agentColors]
              }`}>
                {React.createElement(agentIcons[selectedAgent.role as keyof typeof agentIcons], { className: "w-4 h-4" })}
              </div>
              <div>
                <h3 className="font-semibold text-foreground">{selectedAgent.name}</h3>
                <p className="text-sm text-muted-foreground capitalize">
                  {selectedAgent.role.replace('_', ' ')} • {selectedAgent.status}
                </p>
              </div>
            </div>
            <Button variant="ghost" size="sm">
              <MoreHorizontal className="w-4 h-4" />
            </Button>
          </div>
        </div>
      )}

      {/* Messages */}
      <ScrollArea className="flex-1 px-6">
        <div className="py-4 space-y-4">
          {filteredMessages.length === 0 && selectedAgent && (
            <div className="text-center py-8">
              <div className={`w-16 h-16 rounded-full flex items-center justify-center text-white mx-auto mb-4 ${
                agentColors[selectedAgent.role as keyof typeof agentColors]
              }`}>
                {React.createElement(agentIcons[selectedAgent.role as keyof typeof agentIcons], { className: "w-8 h-8" })}
              </div>
              <h3 className="font-semibold text-foreground mb-2">Start chatting with {selectedAgent.name}</h3>
              <p className="text-sm text-muted-foreground max-w-sm mx-auto">
                This agent specializes in {selectedAgent.role.replace('_', ' ')} tasks. Send a message to get started!
              </p>
            </div>
          )}

          {filteredMessages.map((message) => {
            const isAgent = message.agentId === selectedAgent?.id;
            const isSystem = message.type === "system";
            
            if (isSystem) {
              return (
                <div key={message.id} className="flex justify-center">
                  <div className="bg-muted rounded-lg px-3 py-2 text-xs text-muted-foreground">
                    {message.content}
                  </div>
                </div>
              );
            }

            return (
              <div key={message.id} className={`flex space-x-3 ${isAgent ? '' : 'flex-row-reverse space-x-reverse'}`}>
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white flex-shrink-0 ${
                  isAgent && selectedAgent ? agentColors[selectedAgent.role as keyof typeof agentColors] : "bg-primary"
                }`}>
                  {isAgent && selectedAgent ? (
                    React.createElement(agentIcons[selectedAgent.role as keyof typeof agentIcons], { className: "w-4 h-4" })
                  ) : (
                    <div className="w-4 h-4 bg-white rounded-full"></div>
                  )}
                </div>
                <div className="flex-1 max-w-xs">
                  <Card className={`p-3 ${
                    isAgent 
                      ? "bg-muted" 
                      : "bg-primary text-primary-foreground"
                  }`}>
                    <p className="text-sm">{message.content}</p>
                  </Card>
                  <p className="text-xs text-muted-foreground mt-1 px-1">
                    {message.timestamp ? new Date(message.timestamp).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}) : "Now"}
                  </p>
                </div>
              </div>
            );
          })}
          
          {isTyping && selectedAgent && (
            <div className="flex space-x-3">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white flex-shrink-0 ${
                agentColors[selectedAgent.role as keyof typeof agentColors]
              }`}>
                {React.createElement(agentIcons[selectedAgent.role as keyof typeof agentIcons], { className: "w-4 h-4" })}
              </div>
              <div className="flex-1 max-w-xs">
                <Card className="bg-muted p-3">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: "0.1s" }}></div>
                    <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: "0.2s" }}></div>
                  </div>
                </Card>
              </div>
            </div>
          )}
          <div ref={messagesEndRef} />
        </div>
      </ScrollArea>

      {/* Input */}
      <div className="border-t border-border p-4">
        <div className="flex items-center space-x-2">
          <Button variant="ghost" size="sm">
            <Paperclip className="w-4 h-4" />
          </Button>
          <div className="flex-1 relative">
            <Input
              value={messageInput}
              onChange={(e) => setMessageInput(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder={selectedAgent ? `Message ${selectedAgent.name}...` : "Send msg while agent works - all input"}
              className="pr-10 bg-muted/50 border-0 focus-visible:ring-1"
              disabled={sendMessageMutation.isPending}
            />
            <Button 
              size="sm"
              onClick={handleSendMessage}
              disabled={sendMessageMutation.isPending || !messageInput.trim()}
              className="absolute right-1 top-1/2 transform -translate-y-1/2 h-7 w-7 p-0"
            >
              <Send className="w-3 h-3" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}