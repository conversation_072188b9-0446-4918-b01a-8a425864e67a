import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { useTheme } from "./theme-provider";
import { Badge } from "@/components/ui/badge";
import { Plus, Settings, Moon, Sun, Crown, ClipboardCheck, Compass, Code, BarChart } from "lucide-react";
import type { Project, Agent } from "@shared/schema";
import { apiRequest } from "@/lib/queryClient";

interface SidebarProps {
  currentProject: Project | null;
  selectedAgent: Agent | null;
  onSelectAgent: (agent: Agent) => void;
}

const agentIcons = {
  team_leader: Crown,
  product_manager: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  architect: <PERSON><PERSON><PERSON>,
  engineer: <PERSON>,
  data_analyst: <PERSON><PERSON><PERSON>,
};

const agentColors = {
  team_leader: "from-emerald-500 to-emerald-600",
  product_manager: "from-blue-500 to-blue-600",
  architect: "from-violet-500 to-violet-600",
  engineer: "from-orange-500 to-red-500",
  data_analyst: "from-teal-500 to-cyan-600",
};

const statusColors = {
  active: "bg-green-500",
  processing: "bg-green-500 animate-pulse",
  waiting: "bg-yellow-500",
  idle: "bg-gray-400",
};

export function Sidebar({ currentProject, selectedAgent, onSelectAgent }: SidebarProps) {
  const { theme, setTheme } = useTheme();
  const queryClient = useQueryClient();

  const { data: agents = [] } = useQuery<Agent[]>({
    queryKey: ["/api/projects/1/agents"],
    enabled: !!currentProject,
  });

  const updateAgentMutation = useMutation({
    mutationFn: async ({ id, updates }: { id: number; updates: Partial<Agent> }) => {
      const res = await apiRequest("PATCH", `/api/agents/${id}`, updates);
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/projects/1/agents"] });
    },
  });

  const toggleTheme = () => {
    setTheme(theme === "dark" ? "light" : "dark");
  };

  const getStatusText = (agent: Agent) => {
    switch (agent.status) {
      case "active":
        return "Active";
      case "processing":
        return "Processing";
      case "waiting":
        return "Waiting";
      case "idle":
        return "Idle";
      default:
        return "Unknown";
    }
  };

  const getStatusDescription = (agent: Agent) => {
    switch (agent.role) {
      case "team_leader":
        return agent.status === "active" ? "Managing workflow" : "Standby";
      case "product_manager":
        return agent.status === "active" ? "Analyzing requirements" : "Standby";
      case "architect":
        return agent.status === "active" ? "Designing system" : "Standby";
      case "engineer":
        return agent.status === "active" ? "Implementing features" : "Standby";
      case "data_analyst":
        return agent.status === "active" ? "Analyzing data" : "Standby";
      default:
        return "Standby";
    }
  };

  return (
    <aside className="w-64 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-violet-600 rounded-lg flex items-center justify-center">
              <div className="w-4 h-4 bg-white rounded-sm"></div>
            </div>
            <h1 className="text-xl font-bold text-gray-900 dark:text-white">AgentFlow</h1>
          </div>
          <Button variant="ghost" size="sm" onClick={toggleTheme}>
            {theme === "dark" ? <Sun className="h-4 w-4" /> : <Moon className="h-4 w-4" />}
          </Button>
        </div>
      </div>

      {/* Project Selector */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Current Project</span>
          <Button variant="ghost" size="sm" className="text-xs text-blue-600 dark:text-blue-400">
            Switch
          </Button>
        </div>
        {currentProject && (
          <Card className="bg-gray-50 dark:bg-gray-700 p-3">
            <h3 className="font-medium text-gray-900 dark:text-white">{currentProject.name}</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              {agents.length} agents active
            </p>
          </Card>
        )}
      </div>

      {/* Agent List */}
      <div className="flex-1 p-4">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-sm font-semibold text-gray-900 dark:text-white uppercase tracking-wide">
            Active Agents
          </h2>
          <Button variant="ghost" size="sm">
            <Plus className="h-4 w-4" />
          </Button>
        </div>

        <div className="space-y-2">
          {agents.map((agent) => {
            const IconComponent = agentIcons[agent.role as keyof typeof agentIcons];
            const isSelected = selectedAgent?.id === agent.id;
            
            return (
              <div
                key={agent.id}
                className={`flex items-center space-x-3 p-3 rounded-lg cursor-pointer transition-colors ${
                  isSelected 
                    ? "bg-blue-50 dark:bg-blue-900/20" 
                    : "hover:bg-gray-50 dark:hover:bg-gray-700"
                }`}
                onClick={() => onSelectAgent(agent)}
              >
                <div className="relative">
                  <div className={`w-10 h-10 bg-gradient-to-br ${agentColors[agent.role as keyof typeof agentColors]} rounded-full flex items-center justify-center`}>
                    <IconComponent className="w-5 h-5 text-white" />
                  </div>
                  <div className={`absolute -bottom-1 -right-1 w-4 h-4 ${statusColors[agent.status as keyof typeof statusColors]} border-2 border-white dark:border-gray-800 rounded-full`}></div>
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                    {agent.name}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {getStatusDescription(agent)}
                  </p>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Footer */}
      <div className="p-4 border-t border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
            <div className="w-4 h-4 bg-gray-600 dark:bg-gray-400 rounded-full"></div>
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-gray-900 dark:text-white truncate">Personal Account</p>
            <p className="text-xs text-gray-500 dark:text-gray-400">Free Plan</p>
          </div>
          <Button variant="ghost" size="sm">
            <Settings className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </aside>
  );
}
