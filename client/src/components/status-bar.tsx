import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Play, Pause, Volume2, Settings } from "lucide-react";

interface StatusBarProps {
  isLive?: boolean;
}

export function StatusBar({ isLive = true }: StatusBarProps) {
  return (
    <div className="border-t border-border bg-background px-6 py-3 flex items-center justify-between text-sm">
      <div className="flex items-center space-x-4">
        <Button variant="ghost" size="sm" className="text-muted-foreground">
          <div className="w-2 h-2 bg-current rounded-full mr-2"></div>
          Add
        </Button>
        <Button variant="ghost" size="sm" className="text-muted-foreground">
          <Volume2 className="w-4 h-4 mr-2" />
          claude-3.7-sonnet
        </Button>
      </div>
      
      <div className="flex items-center space-x-4">
        <div className="flex items-center space-x-2">
          <Button variant="ghost" size="sm">
            <Play className="w-4 h-4" />
          </Button>
          <Button variant="ghost" size="sm">
            <Pause className="w-4 h-4" />
          </Button>
        </div>
        
        <div className="flex items-center space-x-2">
          {isLive && (
            <Badge variant="default" className="bg-red-500 hover:bg-red-600 text-white">
              ● LIVE
            </Badge>
          )}
          <span className="text-muted-foreground">Versions</span>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button variant="ghost" size="sm">
            <Settings className="w-4 h-4" />
          </Button>
          <Button variant="ghost" size="sm">
            <Volume2 className="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}