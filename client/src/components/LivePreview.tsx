import React, { useState, useEffect, useRef } from "react";
import { useMutation, useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { 
  Play, 
  Square, 
  RefreshCw, 
  Monitor, 
  Smartphone, 
  Tablet,
  Eye,
  Code,
  Settings,
  ExternalLink,
  Download
} from "lucide-react";
import ApiClient from "@/lib/api";

interface LivePreviewProps {
  projectId: number;
}

interface PreviewFrame {
  id: string;
  name: string;
  width: number;
  height: number;
  icon: React.ReactNode;
}

const previewFrames: PreviewFrame[] = [
  { id: "desktop", name: "Desktop", width: 1200, height: 800, icon: <Monitor className="h-4 w-4" /> },
  { id: "tablet", name: "Tablet", width: 768, height: 1024, icon: <Tablet className="h-4 w-4" /> },
  { id: "mobile", name: "Mobile", width: 375, height: 667, icon: <Smartphone className="h-4 w-4" /> },
];

export function LivePreview({ projectId }: LivePreviewProps) {
  const [isRunning, setIsRunning] = useState(false);
  const [selectedFrame, setSelectedFrame] = useState<PreviewFrame>(previewFrames[0]);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [logs, setLogs] = useState<string[]>([]);
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const wsRef = useRef<WebSocket | null>(null);

  const { data: deployments } = useQuery({
    queryKey: ["deployments", projectId],
    queryFn: () => ApiClient.getAppDeployments(projectId),
  });

  const deployMutation = useMutation({
    mutationFn: () => ApiClient.deployApp(projectId, "preview"),
    onSuccess: (deployment) => {
      setPreviewUrl(deployment.deploymentUrl || null);
      setIsRunning(true);
      addLog("App deployed successfully");
    },
    onError: (error) => {
      addLog(`Deployment failed: ${error.message}`);
    },
  });

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev.slice(-49), `[${timestamp}] ${message}`]);
  };

  useEffect(() => {
    // Connect to WebSocket for real-time updates
    const ws = new WebSocket(`ws://localhost:3000/ws`);
    wsRef.current = ws;

    ws.onmessage = (event) => {
      const data = JSON.parse(event.data);
      if (data.type === 'app_deployed' && data.deployment.projectId === projectId) {
        setPreviewUrl(data.deployment.deploymentUrl);
        setIsRunning(true);
        addLog("Live preview updated");
      }
    };

    return () => {
      ws.close();
    };
  }, [projectId]);

  const handleStart = () => {
    addLog("Starting live preview...");
    deployMutation.mutate();
  };

  const handleStop = () => {
    setIsRunning(false);
    setPreviewUrl(null);
    addLog("Live preview stopped");
  };

  const handleRefresh = () => {
    if (iframeRef.current) {
      iframeRef.current.src = iframeRef.current.src;
      addLog("Preview refreshed");
    }
  };

  const handleFrameChange = (frame: PreviewFrame) => {
    setSelectedFrame(frame);
    addLog(`Switched to ${frame.name} view`);
  };

  const getFrameStyle = () => ({
    width: selectedFrame.width,
    height: selectedFrame.height,
    maxWidth: "100%",
    maxHeight: "70vh",
    border: "1px solid #e2e8f0",
    borderRadius: "8px",
    backgroundColor: "#fff",
  });

  return (
    <div className="space-y-6">
      {/* Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Eye className="h-5 w-5" />
              Live Preview
            </div>
            <div className="flex items-center gap-2">
              <Badge variant={isRunning ? "default" : "secondary"}>
                {isRunning ? "Running" : "Stopped"}
              </Badge>
              {previewUrl && (
                <Button size="sm" variant="outline" asChild>
                  <a href={previewUrl} target="_blank" rel="noopener noreferrer">
                    <ExternalLink className="h-4 w-4 mr-1" />
                    Open
                  </a>
                </Button>
              )}
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-2 mb-4">
            <Button
              onClick={handleStart}
              disabled={isRunning || deployMutation.isPending}
              size="sm"
            >
              {deployMutation.isPending ? (
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Play className="h-4 w-4 mr-2" />
              )}
              Start Preview
            </Button>
            
            <Button
              onClick={handleStop}
              disabled={!isRunning}
              variant="outline"
              size="sm"
            >
              <Square className="h-4 w-4 mr-2" />
              Stop
            </Button>
            
            <Button
              onClick={handleRefresh}
              disabled={!isRunning}
              variant="outline"
              size="sm"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>

            <div className="ml-auto flex items-center gap-2">
              {previewFrames.map((frame) => (
                <Button
                  key={frame.id}
                  onClick={() => handleFrameChange(frame)}
                  variant={selectedFrame.id === frame.id ? "default" : "outline"}
                  size="sm"
                >
                  {frame.icon}
                  <span className="ml-1">{frame.name}</span>
                </Button>
              ))}
            </div>
          </div>

          <div className="text-sm text-gray-600">
            Current frame: {selectedFrame.width} × {selectedFrame.height}px
          </div>
        </CardContent>
      </Card>

      {/* Preview Area */}
      <Card>
        <CardContent className="p-6">
          <div className="flex justify-center">
            {previewUrl ? (
              <div className="relative">
                <iframe
                  ref={iframeRef}
                  src={previewUrl}
                  style={getFrameStyle()}
                  title="Live Preview"
                  sandbox="allow-scripts allow-same-origin allow-forms"
                />
                <div className="absolute top-2 right-2 bg-black bg-opacity-50 text-white px-2 py-1 rounded text-xs">
                  {selectedFrame.name}
                </div>
              </div>
            ) : (
              <div 
                style={getFrameStyle()}
                className="flex items-center justify-center bg-gray-50 text-gray-500"
              >
                <div className="text-center">
                  <Monitor className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p className="text-lg font-medium">No Preview Available</p>
                  <p className="text-sm">Start the live preview to see your app</p>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Tabs for additional info */}
      <Card>
        <CardContent className="p-0">
          <Tabs defaultValue="logs" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="logs">Console Logs</TabsTrigger>
              <TabsTrigger value="deployments">Deployments</TabsTrigger>
              <TabsTrigger value="settings">Settings</TabsTrigger>
              <TabsTrigger value="analytics">Analytics</TabsTrigger>
            </TabsList>
            
            <TabsContent value="logs" className="p-4">
              <div className="bg-black text-green-400 p-4 rounded-lg font-mono text-sm h-64 overflow-auto">
                {logs.length === 0 ? (
                  <div className="text-gray-500">No logs yet...</div>
                ) : (
                  logs.map((log, index) => (
                    <div key={index} className="mb-1">
                      {log}
                    </div>
                  ))
                )}
              </div>
            </TabsContent>
            
            <TabsContent value="deployments" className="p-4">
              <div className="space-y-4">
                {deployments?.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    No deployments yet
                  </div>
                ) : (
                  deployments?.map((deployment) => (
                    <div key={deployment.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-2">
                          <Badge variant="outline">v{deployment.version}</Badge>
                          <Badge 
                            className={
                              deployment.status === "deployed" 
                                ? "bg-green-100 text-green-800"
                                : deployment.status === "failed"
                                ? "bg-red-100 text-red-800"
                                : "bg-blue-100 text-blue-800"
                            }
                          >
                            {deployment.status}
                          </Badge>
                        </div>
                        <div className="flex items-center gap-2">
                          {deployment.deploymentUrl && (
                            <Button size="sm" variant="outline" asChild>
                              <a href={deployment.deploymentUrl} target="_blank" rel="noopener noreferrer">
                                <ExternalLink className="h-4 w-4" />
                              </a>
                            </Button>
                          )}
                          <Button size="sm" variant="outline">
                            <Download className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                      <div className="text-sm text-gray-600">
                        Deployed {new Date(deployment.createdAt).toLocaleString()}
                      </div>
                    </div>
                  ))
                )}
              </div>
            </TabsContent>
            
            <TabsContent value="settings" className="p-4">
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium mb-2">Preview Settings</h4>
                  <div className="space-y-2">
                    <label className="flex items-center gap-2">
                      <input type="checkbox" defaultChecked />
                      <span className="text-sm">Auto-refresh on code changes</span>
                    </label>
                    <label className="flex items-center gap-2">
                      <input type="checkbox" defaultChecked />
                      <span className="text-sm">Show console logs</span>
                    </label>
                    <label className="flex items-center gap-2">
                      <input type="checkbox" />
                      <span className="text-sm">Enable hot reload</span>
                    </label>
                  </div>
                </div>
                
                <div>
                  <h4 className="font-medium mb-2">Performance</h4>
                  <div className="space-y-2">
                    <label className="flex items-center gap-2">
                      <input type="checkbox" defaultChecked />
                      <span className="text-sm">Enable performance monitoring</span>
                    </label>
                    <label className="flex items-center gap-2">
                      <input type="checkbox" />
                      <span className="text-sm">Simulate slow network</span>
                    </label>
                  </div>
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="analytics" className="p-4">
              <div className="grid grid-cols-2 gap-4">
                <Card>
                  <CardContent className="p-4">
                    <div className="text-2xl font-bold">0</div>
                    <div className="text-sm text-gray-600">Page Views</div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardContent className="p-4">
                    <div className="text-2xl font-bold">0ms</div>
                    <div className="text-sm text-gray-600">Load Time</div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardContent className="p-4">
                    <div className="text-2xl font-bold">0</div>
                    <div className="text-sm text-gray-600">Errors</div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardContent className="p-4">
                    <div className="text-2xl font-bold">100%</div>
                    <div className="text-sm text-gray-600">Uptime</div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
