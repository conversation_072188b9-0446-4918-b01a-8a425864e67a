import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Crown, ClipboardCheck, Compass, Code, Bar<PERSON>hart, Plus, Play } from "lucide-react";
import type { Agent } from "@shared/schema";

interface TeamHeaderProps {
  projectId: number;
  selectedAgent: Agent | null;
  onSelectAgent: (agent: Agent) => void;
  onRunWorkflow: () => void;
}

const agentIcons = {
  team_leader: <PERSON>,
  product_manager: Clip<PERSON><PERSON><PERSON><PERSON>,
  architect: Co<PERSON><PERSON>,
  engineer: <PERSON>,
  data_analyst: <PERSON><PERSON><PERSON>,
};

const agentColors = {
  team_leader: "bg-emerald-500",
  product_manager: "bg-blue-500",
  architect: "bg-violet-500",
  engineer: "bg-orange-500",
  data_analyst: "bg-teal-500",
};

const roleLabels = {
  team_leader: "Team Leader",
  product_manager: "Product Manager",
  architect: "Architect",
  engineer: "Engineer",
  data_analyst: "Data Analyst",
};

export function TeamHeader({ projectId, selectedAgent, onSelectAgent, onRunWorkflow }: TeamHeaderProps) {
  const { data: agents = [] } = useQuery<Agent[]>({
    queryKey: [`/api/projects/${projectId}/agents`],
  });

  return (
    <div className="border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="flex items-center justify-between px-6 py-4">
        {/* Left: Team Members */}
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <div className="flex items-center space-x-1">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-sm font-medium text-muted-foreground">Team</span>
              <Badge variant="secondary" className="text-xs px-2">
                {agents.length}
              </Badge>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            {agents.map((agent) => {
              const IconComponent = agentIcons[agent.role as keyof typeof agentIcons];
              const isSelected = selectedAgent?.id === agent.id;
              const isActive = agent.status === "active" || agent.status === "processing";
              
              return (
                <div
                  key={agent.id}
                  className={`relative group cursor-pointer agent-avatar ${
                    isSelected ? "scale-110" : ""
                  }`}
                  onClick={() => onSelectAgent(agent)}
                >
                  <div
                    className={`w-10 h-10 rounded-full flex items-center justify-center text-white font-medium text-sm relative glow-effect ${
                      agentColors[agent.role as keyof typeof agentColors]
                    } ${isSelected ? "ring-2 ring-primary ring-offset-2 ring-offset-background" : ""}`}
                  >
                    <IconComponent className="w-5 h-5" />
                    {isActive && (
                      <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 border-2 border-background rounded-full animate-pulse"></div>
                    )}
                  </div>
                  
                  {/* Tooltip */}
                  <div className="absolute top-12 left-1/2 transform -translate-x-1/2 bg-popover text-popover-foreground px-3 py-2 rounded-lg text-xs whitespace-nowrap opacity-0 group-hover:opacity-100 transition-all duration-300 z-50 shadow-xl border glow-effect scale-95 group-hover:scale-100">
                    <div className="font-medium">{agent.name}</div>
                    <div className="text-muted-foreground">{roleLabels[agent.role as keyof typeof roleLabels]}</div>
                    <div className="absolute -top-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-popover border-l border-t border-border rotate-45"></div>
                  </div>
                </div>
              );
            })}
            
            <Button variant="ghost" size="sm" className="w-10 h-10 rounded-full">
              <Plus className="w-4 h-4" />
            </Button>
          </div>
        </div>
        
        {/* Right: Actions */}
        <div className="flex items-center space-x-3">
          <Button variant="outline" size="sm">
            Share
          </Button>
          <Button size="sm" onClick={onRunWorkflow} className="bg-primary hover:bg-primary/90">
            <Play className="w-4 h-4 mr-2" />
            Run Workflow
          </Button>
        </div>
      </div>
    </div>
  );
}