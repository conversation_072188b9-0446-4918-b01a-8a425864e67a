import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { 
  Users, 
  MessageCircle, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  TrendingUp,
  GitBranch,
  Zap,
  Brain,
  Target
} from 'lucide-react';
import type { Agent, AgentCollaboration, Task } from '@shared/schema';

interface AgentCollaborationPanelProps {
  projectId: number;
  agents: Agent[];
}

export function AgentCollaborationPanel({ projectId, agents }: AgentCollaborationPanelProps) {
  const [activeTab, setActiveTab] = useState('overview');

  // Mock data for demonstration - in production, this would come from API
  const collaborations = [
    {
      id: 1,
      type: 'negotiation',
      subject: 'UI Component Performance Trade-off',
      initiatorAgent: agents.find(a => a.role === 'frontend_developer'),
      targetAgent: agents.find(a => a.role === 'performance_engineer'),
      status: 'in_progress',
      data: {
        issue: 'Complex animation causing 60fps drops',
        proposal: 'Simplify animation or implement GPU acceleration',
        priority: 'high'
      }
    },
    {
      id: 2,
      type: 'resource_request',
      subject: 'Database Schema Optimization',
      initiatorAgent: agents.find(a => a.role === 'backend_developer'),
      targetAgent: agents.find(a => a.role === 'data_analyst'),
      status: 'resolved',
      data: {
        resource: 'Query optimization expertise',
        outcome: 'Implemented indexing strategy'
      }
    }
  ];

  const taskBreakdown = [
    {
      id: 1,
      title: 'User Authentication System',
      priority: 'high',
      status: 'in_progress',
      assignedAgent: 'Backend Developer',
      dependencies: ['Database Schema', 'Security Review'],
      progress: 75,
      estimatedHours: 12,
      actualHours: 9
    },
    {
      id: 2,
      title: 'Responsive Dashboard UI',
      priority: 'medium',
      status: 'pending',
      assignedAgent: 'Frontend Developer',
      dependencies: ['API Endpoints', 'Design System'],
      progress: 30,
      estimatedHours: 16,
      actualHours: 5
    },
    {
      id: 3,
      title: 'Performance Monitoring',
      priority: 'low',
      status: 'completed',
      assignedAgent: 'DevOps Engineer',
      dependencies: [],
      progress: 100,
      estimatedHours: 6,
      actualHours: 4
    }
  ];

  const learningInsights = [
    {
      category: 'Success Pattern',
      insight: 'Projects with early UI/UX agent involvement have 40% fewer revision cycles',
      confidence: 92,
      impact: 'high'
    },
    {
      category: 'Performance Data',
      insight: 'API-first development reduces integration issues by 65%',
      confidence: 88,
      impact: 'medium'
    },
    {
      category: 'User Feedback',
      insight: 'Real-time preview increases user satisfaction by 80%',
      confidence: 95,
      impact: 'high'
    }
  ];

  const renderCollaborationOverview = () => (
    <div className="space-y-6">
      <Card className="p-6 glow-effect">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <Users className="w-5 h-5 text-primary" />
            Active Collaborations
          </h3>
          <Badge variant="secondary">{collaborations.length} ongoing</Badge>
        </div>
        
        <div className="space-y-3">
          {collaborations.map((collab) => (
            <div key={collab.id} className="border rounded-lg p-4 fade-in">
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-medium">{collab.subject}</h4>
                <Badge 
                  variant={collab.status === 'resolved' ? 'default' : 'secondary'}
                  className="capitalize"
                >
                  {collab.status}
                </Badge>
              </div>
              <div className="flex items-center gap-4 text-sm text-muted-foreground">
                <span>{collab.initiatorAgent?.name}</span>
                <GitBranch className="w-4 h-4" />
                <span>{collab.targetAgent?.name}</span>
              </div>
              {collab.type === 'negotiation' && (
                <div className="mt-3 p-3 bg-muted/50 rounded">
                  <p className="text-sm">{collab.data.issue}</p>
                  <p className="text-sm font-medium mt-1">{collab.data.proposal}</p>
                </div>
              )}
            </div>
          ))}
        </div>
      </Card>

      <Card className="p-6 glow-effect">
        <h3 className="text-lg font-semibold flex items-center gap-2 mb-4">
          <Brain className="w-5 h-5 text-primary" />
          AI Learning Insights
        </h3>
        
        <div className="space-y-3">
          {learningInsights.map((insight, index) => (
            <div key={index} className="border-l-4 border-primary pl-4 fade-in" style={{animationDelay: `${index * 0.1}s`}}>
              <div className="flex items-center justify-between mb-1">
                <Badge variant="outline">{insight.category}</Badge>
                <div className="flex items-center gap-2">
                  <span className="text-xs text-muted-foreground">{insight.confidence}% confidence</span>
                  <Badge variant={insight.impact === 'high' ? 'default' : 'secondary'}>
                    {insight.impact} impact
                  </Badge>
                </div>
              </div>
              <p className="text-sm">{insight.insight}</p>
            </div>
          ))}
        </div>
      </Card>
    </div>
  );

  const renderTaskManagement = () => (
    <div className="space-y-6">
      <Card className="p-6 glow-effect">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <Target className="w-5 h-5 text-primary" />
            Intelligent Task Breakdown
          </h3>
          <Button variant="outline" size="sm">
            <Zap className="w-4 h-4 mr-2" />
            Auto-Generate Tasks
          </Button>
        </div>
        
        <div className="space-y-4">
          {taskBreakdown.map((task, index) => (
            <div key={task.id} className="border rounded-lg p-4 card-hover fade-in" style={{animationDelay: `${index * 0.1}s`}}>
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-medium">{task.title}</h4>
                <div className="flex items-center gap-2">
                  <Badge 
                    variant={task.priority === 'high' ? 'destructive' : task.priority === 'medium' ? 'default' : 'secondary'}
                  >
                    {task.priority}
                  </Badge>
                  <Badge 
                    variant={task.status === 'completed' ? 'default' : 'outline'}
                    className="capitalize"
                  >
                    {task.status === 'in_progress' ? (
                      <><Clock className="w-3 h-3 mr-1" /> In Progress</>
                    ) : task.status === 'completed' ? (
                      <><CheckCircle className="w-3 h-3 mr-1" /> Done</>
                    ) : (
                      'Pending'
                    )}
                  </Badge>
                </div>
              </div>
              
              <div className="mb-3">
                <div className="flex justify-between text-sm mb-1">
                  <span>Progress</span>
                  <span>{task.progress}%</span>
                </div>
                <Progress value={task.progress} className="h-2" />
              </div>
              
              <div className="flex items-center justify-between text-sm text-muted-foreground">
                <span>Assigned to: {task.assignedAgent}</span>
                <span>{task.actualHours}h / {task.estimatedHours}h est.</span>
              </div>
              
              {task.dependencies.length > 0 && (
                <div className="mt-2">
                  <span className="text-xs text-muted-foreground">Dependencies:</span>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {task.dependencies.map((dep, i) => (
                      <Badge key={i} variant="outline" className="text-xs">
                        {dep}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      </Card>
    </div>
  );

  const renderPerformanceMetrics = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="p-4 glow-effect text-center">
          <TrendingUp className="w-8 h-8 text-green-500 mx-auto mb-2" />
          <div className="text-2xl font-bold">94%</div>
          <div className="text-sm text-muted-foreground">Task Success Rate</div>
        </Card>
        <Card className="p-4 glow-effect text-center">
          <Clock className="w-8 h-8 text-blue-500 mx-auto mb-2" />
          <div className="text-2xl font-bold">2.3h</div>
          <div className="text-sm text-muted-foreground">Avg Resolution Time</div>
        </Card>
        <Card className="p-4 glow-effect text-center">
          <Users className="w-8 h-8 text-purple-500 mx-auto mb-2" />
          <div className="text-2xl font-bold">12</div>
          <div className="text-sm text-muted-foreground">Active Collaborations</div>
        </Card>
      </div>
      
      <Card className="p-6 glow-effect">
        <h3 className="text-lg font-semibold mb-4">Agent Performance Analytics</h3>
        <div className="space-y-4">
          {agents.map((agent, index) => (
            <div key={agent.id} className="flex items-center justify-between p-3 border rounded-lg fade-in" style={{animationDelay: `${index * 0.1}s`}}>
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
                  <span className="text-sm font-medium">{agent.name.charAt(0)}</span>
                </div>
                <div>
                  <div className="font-medium">{agent.name}</div>
                  <div className="text-sm text-muted-foreground capitalize">{agent.role.replace('_', ' ')}</div>
                </div>
              </div>
              <div className="text-right">
                <div className="text-sm font-medium">98% Accuracy</div>
                <div className="text-xs text-muted-foreground">15 tasks completed</div>
              </div>
            </div>
          ))}
        </div>
      </Card>
    </div>
  );

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">Agent Collaboration Hub</h2>
        <p className="text-muted-foreground">
          Monitor and manage intelligent agent interactions, task coordination, and performance analytics
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">Collaboration Overview</TabsTrigger>
          <TabsTrigger value="tasks">Task Management</TabsTrigger>
          <TabsTrigger value="analytics">Performance Analytics</TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview" className="mt-6">
          {renderCollaborationOverview()}
        </TabsContent>
        
        <TabsContent value="tasks" className="mt-6">
          {renderTaskManagement()}
        </TabsContent>
        
        <TabsContent value="analytics" className="mt-6">
          {renderPerformanceMetrics()}
        </TabsContent>
      </Tabs>
    </div>
  );
}