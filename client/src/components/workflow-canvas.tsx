import { useState, useRef, useCallback } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Plus, ZoomIn, ZoomOut, Grid, Play } from "lucide-react";
import { Crown, ClipboardCheck, Compass, Code, BarChart } from "lucide-react";
import type { WorkflowNode, WorkflowConnection, Agent } from "@shared/schema";
import { apiRequest } from "@/lib/queryClient";

interface WorkflowCanvasProps {
  projectId: number;
}

const agentIcons = {
  team_leader: Crown,
  product_manager: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  architect: <PERSON><PERSON><PERSON>,
  engineer: <PERSON>,
  data_analyst: <PERSON><PERSON><PERSON>,
};

const agentColors = {
  team_leader: "border-emerald-500",
  product_manager: "border-blue-500",
  architect: "border-violet-500",
  engineer: "border-orange-500",
  data_analyst: "border-teal-500",
};

const statusColors = {
  active: "bg-green-500",
  processing: "bg-green-500 animate-pulse",
  waiting: "bg-yellow-500",
  idle: "bg-gray-400",
};

export function WorkflowCanvas({ projectId }: WorkflowCanvasProps) {
  const [zoom, setZoom] = useState(1);
  const [pan, setPan] = useState({ x: 0, y: 0 });
  const [selectedNode, setSelectedNode] = useState<WorkflowNode | null>(null);
  const canvasRef = useRef<HTMLDivElement>(null);
  const queryClient = useQueryClient();

  const { data: nodes = [] } = useQuery<WorkflowNode[]>({
    queryKey: [`/api/projects/${projectId}/workflow/nodes`],
  });

  const { data: connections = [] } = useQuery<WorkflowConnection[]>({
    queryKey: [`/api/projects/${projectId}/workflow/connections`],
  });

  const { data: agents = [] } = useQuery<Agent[]>({
    queryKey: [`/api/projects/${projectId}/agents`],
  });

  const updateNodeMutation = useMutation({
    mutationFn: async ({ id, updates }: { id: number; updates: Partial<WorkflowNode> }) => {
      const res = await apiRequest("PATCH", `/api/workflow/nodes/${id}`, updates);
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/projects/${projectId}/workflow/nodes`] });
    },
  });

  const runWorkflowMutation = useMutation({
    mutationFn: async () => {
      const res = await apiRequest("POST", `/api/projects/${projectId}/run`);
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/projects/${projectId}/agents`] });
    },
  });

  const handleZoomIn = () => setZoom(prev => Math.min(prev + 0.1, 2));
  const handleZoomOut = () => setZoom(prev => Math.max(prev - 0.1, 0.5));

  const handleNodeDrag = useCallback((nodeId: number, newPosition: { x: number; y: number }) => {
    updateNodeMutation.mutate({
      id: nodeId,
      updates: { position: newPosition }
    });
  }, [updateNodeMutation]);

  const getAgentForNode = (node: WorkflowNode) => {
    if (!node.agentId) return null;
    return agents.find(agent => agent.id === node.agentId);
  };

  const renderNode = (node: WorkflowNode) => {
    const agent = getAgentForNode(node);
    const position = node.position as { x: number; y: number };

    if (node.type === "start") {
      return (
        <div
          key={node.id}
          className="absolute cursor-move"
          style={{
            left: position.x,
            top: position.y,
            transform: `scale(${zoom})`,
            transformOrigin: "top left"
          }}
          onClick={() => setSelectedNode(node)}
        >
          <Card className="bg-white dark:bg-gray-800 border-2 border-emerald-500 p-4 w-48 shadow-lg">
            <div className="flex items-center space-x-3 mb-3">
              <div className="w-8 h-8 bg-emerald-500 rounded-full flex items-center justify-center">
                <Play className="w-4 h-4 text-white" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 dark:text-white text-sm">Project Start</h3>
                <p className="text-xs text-gray-500 dark:text-gray-400">Initialize workflow</p>
              </div>
            </div>
            <div className="text-xs text-gray-600 dark:text-gray-400">
              Triggers: Manual start
            </div>
          </Card>
        </div>
      );
    }

    if (node.type === "agent" && agent) {
      const IconComponent = agentIcons[agent.role as keyof typeof agentIcons];
      const borderColor = agentColors[agent.role as keyof typeof agentColors];

      return (
        <div
          key={node.id}
          className="absolute cursor-move"
          style={{
            left: position.x,
            top: position.y,
            transform: `scale(${zoom})`,
            transformOrigin: "top left"
          }}
          onClick={() => setSelectedNode(node)}
        >
          <Card className={`bg-white dark:bg-gray-800 border-2 ${borderColor} p-4 w-56 shadow-lg`}>
            <div className="flex items-center space-x-3 mb-3">
              <div className={`w-8 h-8 bg-gradient-to-br ${agentColors[agent.role as keyof typeof agentColors].replace('border-', 'from-')} to-${agentColors[agent.role as keyof typeof agentColors].split('-')[1]}-600 rounded-full flex items-center justify-center`}>
                <IconComponent className="w-4 h-4 text-white" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 dark:text-white text-sm">{agent.name}</h3>
                <p className="text-xs text-gray-500 dark:text-gray-400">{agent.role.replace('_', ' ')}</p>
              </div>
            </div>
            <div className="space-y-2">
              <div className="text-xs text-gray-600 dark:text-gray-400">
                • {agent.role === "team_leader" ? "Coordinate workflow" : "Execute tasks"}
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-400">
                • {agent.role === "product_manager" ? "Analyze requirements" : "Process data"}
              </div>
              <div className="flex items-center space-x-2 mt-3">
                <div className={`w-2 h-2 ${statusColors[agent.status as keyof typeof statusColors]} rounded-full`}></div>
                <span className="text-xs text-gray-600 dark:text-gray-400 capitalize">{agent.status}</span>
              </div>
            </div>
          </Card>
        </div>
      );
    }

    return null;
  };

  const renderConnections = () => {
    return (
      <svg 
        className="absolute inset-0 pointer-events-none" 
        style={{ zIndex: 5 }}
        width="100%" 
        height="100%"
      >
        {connections.map((connection) => {
          const sourceNode = nodes.find(n => n.id === connection.sourceNodeId);
          const targetNode = nodes.find(n => n.id === connection.targetNodeId);
          
          if (!sourceNode || !targetNode) return null;

          const sourcePos = sourceNode.position as { x: number; y: number };
          const targetPos = targetNode.position as { x: number; y: number };

          const startX = (sourcePos.x + 120) * zoom; // Center of source node
          const startY = (sourcePos.y + 40) * zoom;
          const endX = (targetPos.x + 120) * zoom; // Center of target node
          const endY = (targetPos.y + 40) * zoom;

          const midX = (startX + endX) / 2;
          const midY = (startY + endY) / 2;

          return (
            <path
              key={connection.id}
              d={`M ${startX} ${startY} Q ${midX} ${midY - 50} ${endX} ${endY}`}
              stroke="#3b82f6"
              strokeWidth="2"
              fill="none"
              className="opacity-75"
            />
          );
        })}
      </svg>
    );
  };

  return (
    <div className="flex-1 bg-gray-50 dark:bg-gray-900 relative overflow-hidden">
      {/* Grid Background */}
      <div 
        className="absolute inset-0 opacity-25 dark:opacity-10"
        style={{
          backgroundImage: "radial-gradient(circle, #6b7280 1px, transparent 1px)",
          backgroundSize: `${20 * zoom}px ${20 * zoom}px`,
          backgroundPosition: `${pan.x}px ${pan.y}px`
        }}
      ></div>

      {/* Canvas */}
      <div 
        ref={canvasRef}
        className="relative z-10 w-full h-full overflow-auto"
        style={{ transform: `translate(${pan.x}px, ${pan.y}px)` }}
      >
        {/* Workflow Nodes */}
        <div className="relative min-w-full min-h-full p-8">
          {nodes.map(renderNode)}
          {renderConnections()}
        </div>
      </div>

      {/* Floating Toolbar */}
      <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 z-20">
        <Card className="px-4 py-3">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="sm">
              <Plus className="h-4 w-4" />
            </Button>
            <div className="w-px h-6 bg-gray-300 dark:bg-gray-600"></div>
            <Button variant="ghost" size="sm" onClick={handleZoomIn}>
              <ZoomIn className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={handleZoomOut}>
              <ZoomOut className="h-4 w-4" />
            </Button>
            <div className="w-px h-6 bg-gray-300 dark:bg-gray-600"></div>
            <Button variant="ghost" size="sm">
              <Grid className="h-4 w-4" />
            </Button>
          </div>
        </Card>
      </div>

      {/* Top Bar */}
      <div className="absolute top-0 left-0 right-0 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4 z-10">
        <div className="flex items-center justify-between">
          <nav className="flex space-x-1">
            <Button variant="default" size="sm">
              Workflow
            </Button>
            <Button variant="ghost" size="sm">
              Chat
            </Button>
            <Button variant="ghost" size="sm">
              Analytics
            </Button>
          </nav>
          <div className="flex items-center space-x-3">
            <Button variant="outline" size="sm">
              Save
            </Button>
            <Button 
              size="sm" 
              onClick={() => runWorkflowMutation.mutate()}
              disabled={runWorkflowMutation.isPending}
            >
              <Play className="h-4 w-4 mr-2" />
              {runWorkflowMutation.isPending ? "Starting..." : "Run Workflow"}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
