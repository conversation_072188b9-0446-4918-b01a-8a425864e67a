import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Monitor, 
  Smartphone, 
  Tablet, 
  Play, 
  Pause, 
  RotateCcw, 
  ExternalLink,
  Edit3,
  Zap,
  Eye,
  Settings
} from 'lucide-react';

interface LivePreviewPanelProps {
  projectId: number;
}

export function LivePreviewPanel({ projectId }: LivePreviewPanelProps) {
  const [previewMode, setPreviewMode] = useState<'desktop' | 'tablet' | 'mobile'>('desktop');
  const [isLive, setIsLive] = useState(true);
  const [editMode, setEditMode] = useState(false);

  const getDeviceStyles = () => {
    switch (previewMode) {
      case 'mobile':
        return { width: '375px', height: '667px' };
      case 'tablet':
        return { width: '768px', height: '1024px' };
      default:
        return { width: '100%', height: '600px' };
    }
  };

  const renderPreviewFrame = () => (
    <div className="relative bg-gray-100 dark:bg-gray-900 rounded-lg overflow-hidden glow-effect" style={getDeviceStyles()}>
      {/* Simulated App Preview */}
      <div className="h-full flex flex-col">
        {/* Header */}
        <div className="bg-white dark:bg-gray-800 border-b p-4 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 bg-primary rounded flex items-center justify-center">
              <Zap className="w-5 h-5 text-white" />
            </div>
            <h1 className="font-semibold">Generated App</h1>
          </div>
          <Badge variant="outline">Live Preview</Badge>
        </div>
        
        {/* Main Content */}
        <div className="flex-1 p-6">
          <div className="space-y-6">
            {/* Dashboard Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm">
                <h3 className="font-medium mb-2">Total Users</h3>
                <div className="text-2xl font-bold text-blue-600">1,234</div>
                <div className="text-sm text-green-600">↗ +12% from last month</div>
              </div>
              <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm">
                <h3 className="font-medium mb-2">Revenue</h3>
                <div className="text-2xl font-bold text-green-600">$45,678</div>
                <div className="text-sm text-green-600">↗ +8% from last month</div>
              </div>
              <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm">
                <h3 className="font-medium mb-2">Orders</h3>
                <div className="text-2xl font-bold text-purple-600">567</div>
                <div className="text-sm text-red-600">↘ -3% from last month</div>
              </div>
            </div>
            
            {/* Chart Area */}
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm">
              <h3 className="font-medium mb-4">Analytics Overview</h3>
              <div className="h-32 bg-gradient-to-r from-blue-100 to-purple-100 dark:from-blue-900 dark:to-purple-900 rounded flex items-center justify-center">
                <span className="text-gray-600 dark:text-gray-400">Interactive Chart Component</span>
              </div>
            </div>
            
            {/* Data Table */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
              <div className="p-4 border-b">
                <h3 className="font-medium">Recent Activity</h3>
              </div>
              <div className="divide-y">
                {[1, 2, 3, 4].map((item) => (
                  <div key={item} className="p-4 flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-gray-100 dark:bg-gray-700 rounded-full"></div>
                      <div>
                        <div className="font-medium">User Action {item}</div>
                        <div className="text-sm text-gray-600 dark:text-gray-400">2 hours ago</div>
                      </div>
                    </div>
                    <Badge variant="outline">Active</Badge>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Live Edit Overlay */}
      {editMode && (
        <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
          <Card className="p-6 m-4 max-w-sm">
            <h3 className="font-semibold mb-4">Live Edit Mode</h3>
            <p className="text-sm text-muted-foreground mb-4">
              Click on any element to modify its properties, styling, or content in real-time.
            </p>
            <Button onClick={() => setEditMode(false)} className="w-full">
              Exit Edit Mode
            </Button>
          </Card>
        </div>
      )}
    </div>
  );

  return (
    <div className="max-w-7xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">Live Preview & Interactive Editing</h2>
        <p className="text-muted-foreground">
          Real-time preview of your generated application with live editing capabilities
        </p>
      </div>

      {/* Preview Controls */}
      <Card className="p-4 mb-6 glow-effect">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            {/* Device Toggle */}
            <div className="flex items-center gap-2">
              <Button
                variant={previewMode === 'desktop' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setPreviewMode('desktop')}
              >
                <Monitor className="w-4 h-4" />
              </Button>
              <Button
                variant={previewMode === 'tablet' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setPreviewMode('tablet')}
              >
                <Tablet className="w-4 h-4" />
              </Button>
              <Button
                variant={previewMode === 'mobile' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setPreviewMode('mobile')}
              >
                <Smartphone className="w-4 h-4" />
              </Button>
            </div>

            {/* Live Controls */}
            <div className="flex items-center gap-2">
              <Button
                variant={isLive ? 'default' : 'outline'}
                size="sm"
                onClick={() => setIsLive(!isLive)}
              >
                {isLive ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                {isLive ? 'Live' : 'Paused'}
              </Button>
              <Button variant="outline" size="sm">
                <RotateCcw className="w-4 h-4" />
                Refresh
              </Button>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant={editMode ? 'default' : 'outline'}
              size="sm"
              onClick={() => setEditMode(!editMode)}
            >
              <Edit3 className="w-4 h-4 mr-2" />
              {editMode ? 'Exit Edit' : 'Live Edit'}
            </Button>
            <Button variant="outline" size="sm">
              <ExternalLink className="w-4 h-4 mr-2" />
              Open in New Tab
            </Button>
          </div>
        </div>
      </Card>

      {/* Preview Area */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Main Preview */}
        <div className="lg:col-span-3">
          <Card className="p-6">
            <div className="flex items-center justify-center">
              {renderPreviewFrame()}
            </div>
          </Card>
        </div>

        {/* Property Panel */}
        <div className="space-y-4">
          <Card className="p-4 glow-effect">
            <h3 className="font-semibold flex items-center gap-2 mb-4">
              <Settings className="w-4 h-4" />
              Properties
            </h3>
            
            {editMode ? (
              <div className="space-y-3">
                <div>
                  <label className="text-sm font-medium">Background Color</label>
                  <div className="flex gap-2 mt-1">
                    <div className="w-6 h-6 bg-white border rounded cursor-pointer"></div>
                    <div className="w-6 h-6 bg-gray-100 border rounded cursor-pointer"></div>
                    <div className="w-6 h-6 bg-blue-100 border rounded cursor-pointer"></div>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium">Typography</label>
                  <select className="w-full mt-1 p-2 border rounded text-sm">
                    <option>Inter</option>
                    <option>Roboto</option>
                    <option>Open Sans</option>
                  </select>
                </div>
                <div>
                  <label className="text-sm font-medium">Layout</label>
                  <select className="w-full mt-1 p-2 border rounded text-sm">
                    <option>Grid</option>
                    <option>Flexbox</option>
                    <option>Stack</option>
                  </select>
                </div>
              </div>
            ) : (
              <p className="text-sm text-muted-foreground">
                Enable Live Edit mode to modify component properties
              </p>
            )}
          </Card>

          <Card className="p-4 glow-effect">
            <h3 className="font-semibold flex items-center gap-2 mb-4">
              <Eye className="w-4 h-4" />
              Performance
            </h3>
            <div className="space-y-3">
              <div className="flex justify-between text-sm">
                <span>Load Time</span>
                <span className="text-green-600">1.2s</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Bundle Size</span>
                <span className="text-blue-600">245KB</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Lighthouse Score</span>
                <span className="text-green-600">98/100</span>
              </div>
            </div>
          </Card>

          <Card className="p-4 glow-effect">
            <h3 className="font-semibold mb-4">Recent Changes</h3>
            <div className="space-y-2 text-sm">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span>Updated dashboard layout</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <span>Added chart component</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                <span>Improved responsive design</span>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
}