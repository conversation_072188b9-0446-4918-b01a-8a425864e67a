import { useState, useRef, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Settings, X, Send, Paperclip, AtSign } from "lucide-react";
import { Crown, ClipboardCheck, Compass, Code, BarChart } from "lucide-react";
import type { Agent, Message } from "@shared/schema";
import { apiRequest } from "@/lib/queryClient";
import { sambaNovaClient } from "@/lib/sambanova";

interface AgentPanelProps {
  agent: Agent | null;
  projectId: number;
  onClose: () => void;
}

const agentIcons = {
  team_leader: Crown,
  product_manager: <PERSON>lipboard<PERSON><PERSON><PERSON>,
  architect: <PERSON><PERSON><PERSON>,
  engineer: <PERSON>,
  data_analyst: <PERSON><PERSON><PERSON>,
};

const agentColors = {
  team_leader: "from-emerald-500 to-emerald-600",
  product_manager: "from-blue-500 to-blue-600",
  architect: "from-violet-500 to-violet-600",
  engineer: "from-orange-500 to-red-500",
  data_analyst: "from-teal-500 to-cyan-600",
};

export function AgentPanel({ agent, projectId, onClose }: AgentPanelProps) {
  const [messageInput, setMessageInput] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const queryClient = useQueryClient();

  const { data: messages = [] } = useQuery<Message[]>({
    queryKey: [`/api/projects/${projectId}/messages`],
    refetchInterval: 2000, // Poll for new messages
  });

  const { data: tasks = [] } = useQuery({
    queryKey: [`/api/projects/${projectId}/tasks`],
  });

  const sendMessageMutation = useMutation({
    mutationFn: async (content: string) => {
      // Send user message first
      const userMessage = await apiRequest("POST", "/api/messages", {
        projectId,
        agentId: null,
        content,
        type: "message"
      });

      // Generate AI response using SambaNova
      if (agent) {
        try {
          setIsTyping(true);
          const context = `Project: E-Commerce Platform\nAgent Role: ${agent.role}\nRecent messages: ${messages.slice(-3).map(m => m.content).join('\n')}`;
          const aiResponse = await sambaNovaClient.generateAgentResponse(agent.role, context, content);
          
          // Send AI response
          await apiRequest("POST", "/api/messages", {
            projectId,
            agentId: agent.id,
            content: aiResponse,
            type: "message"
          });
        } catch (error) {
          console.error("Failed to generate AI response:", error);
          // Fallback to simple response
          await apiRequest("POST", "/api/messages", {
            projectId,
            agentId: agent.id,
            content: `As ${agent.name}, I understand your message: "${content}". I'll help you with this task based on my role as ${agent.role.replace('_', ' ')}.`,
            type: "message"
          });
        } finally {
          setIsTyping(false);
        }
      }

      return userMessage.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/projects/${projectId}/messages`] });
      setMessageInput("");
    },
  });

  const handleSendMessage = () => {
    if (messageInput.trim()) {
      sendMessageMutation.mutate(messageInput);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  if (!agent) return null;

  const IconComponent = agentIcons[agent.role as keyof typeof agentIcons];
  const agentTasks = tasks.filter((task: any) => task.agentId === agent.id);
  const activeTasks = agentTasks.filter((task: any) => task.status === "in_progress");

  return (
    <aside className="w-80 bg-white dark:bg-gray-800 border-l border-gray-200 dark:border-gray-700 flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-3">
          <div className={`w-12 h-12 bg-gradient-to-br ${agentColors[agent.role as keyof typeof agentColors]} rounded-xl flex items-center justify-center`}>
            <IconComponent className="w-6 h-6 text-white" />
          </div>
          <div className="flex-1">
            <h3 className="font-semibold text-gray-900 dark:text-white">{agent.name}</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {agent.role.replace('_', ' ')} - {agent.status}
            </p>
          </div>
          <div className="flex space-x-1">
            <Button variant="ghost" size="sm">
              <Settings className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Status */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="grid grid-cols-2 gap-4">
          <Card className={`p-3 ${
            agent.status === "active" ? "bg-green-50 dark:bg-green-900/20" : "bg-gray-50 dark:bg-gray-700"
          }`}>
            <div className="flex items-center space-x-2">
              <div className={`w-3 h-3 rounded-full ${
                agent.status === "active" ? "bg-green-500" : "bg-gray-400"
              }`}></div>
              <span className={`text-sm font-medium ${
                agent.status === "active" ? "text-green-700 dark:text-green-400" : "text-gray-700 dark:text-gray-400"
              }`}>
                {agent.status === "active" ? "Active" : "Inactive"}
              </span>
            </div>
            <p className={`text-xs mt-1 ${
              agent.status === "active" ? "text-green-600 dark:text-green-500" : "text-gray-600 dark:text-gray-500"
            }`}>
              {agent.status === "active" ? "Processing tasks" : "Standby"}
            </p>
          </Card>
          <Card className="bg-blue-50 dark:bg-blue-900/20 p-3">
            <p className="text-sm font-medium text-blue-700 dark:text-blue-400">
              {activeTasks.length} Tasks
            </p>
            <p className="text-xs text-blue-600 dark:text-blue-500 mt-1">In progress</p>
          </Card>
        </div>
      </div>

      {/* Chat */}
      <div className="flex-1 flex flex-col">
        <ScrollArea className="flex-1 p-4">
          <div className="space-y-4">
            {messages
              .filter(message => !message.agentId || message.agentId === agent.id)
              .map((message) => {
                const isAgent = message.agentId === agent.id;
                const isSystem = message.type === "system";
                
                if (isSystem) {
                  return (
                    <div key={message.id} className="flex justify-center">
                      <div className="bg-gray-100 dark:bg-gray-700 rounded-lg px-3 py-2 text-xs text-gray-600 dark:text-gray-400">
                        {message.content}
                      </div>
                    </div>
                  );
                }

                return (
                  <div key={message.id} className="flex space-x-3">
                    <div className={`w-8 h-8 bg-gradient-to-br ${
                      isAgent ? agentColors[agent.role as keyof typeof agentColors] : "from-gray-400 to-gray-500"
                    } rounded-full flex items-center justify-center flex-shrink-0`}>
                      {isAgent ? (
                        <IconComponent className="w-4 h-4 text-white" />
                      ) : (
                        <div className="w-4 h-4 bg-white rounded-full"></div>
                      )}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <span className="text-sm font-medium text-gray-900 dark:text-white">
                          {isAgent ? agent.name : "You"}
                        </span>
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          {message.timestamp ? new Date(message.timestamp).toLocaleTimeString() : "Now"}
                        </span>
                      </div>
                      <div className={`rounded-lg p-3 ${
                        isAgent 
                          ? "bg-blue-50 dark:bg-blue-900/20" 
                          : "bg-gray-100 dark:bg-gray-700"
                      }`}>
                        <p className="text-sm text-gray-900 dark:text-white">{message.content}</p>
                      </div>
                    </div>
                  </div>
                );
              })}
            
            {isTyping && (
              <div className="flex space-x-3">
                <div className={`w-8 h-8 bg-gradient-to-br ${agentColors[agent.role as keyof typeof agentColors]} rounded-full flex items-center justify-center flex-shrink-0`}>
                  <IconComponent className="w-4 h-4 text-white" />
                </div>
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-1">
                    <span className="text-sm font-medium text-gray-900 dark:text-white">{agent.name}</span>
                    <span className="text-xs text-gray-500 dark:text-gray-400">typing...</span>
                  </div>
                  <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3">
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: "0.1s" }}></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: "0.2s" }}></div>
                    </div>
                  </div>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>
        </ScrollArea>

        {/* Input */}
        <div className="p-4 border-t border-gray-200 dark:border-gray-700">
          <div className="flex space-x-3">
            <Input
              value={messageInput}
              onChange={(e) => setMessageInput(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Send a message to the agent..."
              className="flex-1"
              disabled={sendMessageMutation.isPending}
            />
            <Button 
              onClick={handleSendMessage}
              disabled={sendMessageMutation.isPending || !messageInput.trim()}
            >
              <Send className="h-4 w-4" />
            </Button>
          </div>
          <div className="flex items-center space-x-4 mt-3">
            <Button variant="ghost" size="sm" className="text-xs">
              <Paperclip className="h-3 w-3 mr-1" />
              Attach
            </Button>
            <Button variant="ghost" size="sm" className="text-xs">
              <AtSign className="h-3 w-3 mr-1" />
              Mention
            </Button>
          </div>
        </div>
      </div>
    </aside>
  );
}
