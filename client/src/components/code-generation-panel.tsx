import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Code2, 
  Wand2, 
  Download, 
  Eye, 
  Copy,
  Sparkles,
  FileCode,
  Database,
  Palette,
  Settings
} from 'lucide-react';

interface CodeGenerationPanelProps {
  projectId: number;
}

export function CodeGenerationPanel({ projectId }: CodeGenerationPanelProps) {
  const [activeTab, setActiveTab] = useState('generate');
  const [selectedType, setSelectedType] = useState('frontend');
  const [selectedLanguage, setSelectedLanguage] = useState('typescript');
  const [prompt, setPrompt] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);

  const codeTypes = [
    { value: 'frontend', label: 'Frontend Component', icon: Palette },
    { value: 'backend', label: 'Backend API', icon: Settings },
    { value: 'database', label: 'Database Schema', icon: Database },
    { value: 'component', label: 'UI Component', icon: FileCode },
  ];

  const languages = {
    frontend: ['typescript', 'javascript', 'jsx', 'tsx'],
    backend: ['typescript', 'javascript', 'python', 'sql'],
    database: ['sql', 'mongodb', 'postgresql'],
    component: ['typescript', 'jsx', 'tsx', 'css'],
  };

  const recentGenerations = [
    {
      id: 1,
      type: 'frontend',
      language: 'typescript',
      prompt: 'Create a responsive user dashboard with charts',
      generatedCode: `import React from 'react';
import { Card } from '@/components/ui/card';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

export function UserDashboard() {
  const data = [
    { name: 'Jan', value: 400 },
    { name: 'Feb', value: 300 },
    { name: 'Mar', value: 200 },
  ];

  return (
    <div className="p-6 space-y-6">
      <h1 className="text-3xl font-bold">Dashboard</h1>
      <Card className="p-6">
        <h2 className="text-xl font-semibold mb-4">Analytics</h2>
        <ResponsiveContainer width="100%" height={300}>
          <LineChart data={data}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" />
            <YAxis />
            <Tooltip />
            <Line type="monotone" dataKey="value" stroke="#8884d8" />
          </LineChart>
        </ResponsiveContainer>
      </Card>
    </div>
  );
}`,
      quality: 9,
      status: 'integrated',
      createdAt: new Date('2024-01-15'),
    },
    {
      id: 2,
      type: 'backend',
      language: 'typescript',
      prompt: 'Create REST API endpoints for user management',
      generatedCode: `import express from 'express';
import { z } from 'zod';

const userSchema = z.object({
  name: z.string().min(1),
  email: z.string().email(),
  role: z.enum(['admin', 'user']),
});

export function createUserRoutes() {
  const router = express.Router();

  router.get('/users', async (req, res) => {
    try {
      const users = await getUsersFromDB();
      res.json(users);
    } catch (error) {
      res.status(500).json({ error: 'Failed to fetch users' });
    }
  });

  router.post('/users', async (req, res) => {
    try {
      const userData = userSchema.parse(req.body);
      const user = await createUser(userData);
      res.status(201).json(user);
    } catch (error) {
      res.status(400).json({ error: 'Invalid user data' });
    }
  });

  return router;
}`,
      quality: 8,
      status: 'reviewed',
      createdAt: new Date('2024-01-14'),
    },
  ];

  const handleGenerate = async () => {
    if (!prompt.trim()) return;
    
    setIsGenerating(true);
    // Simulate AI generation
    await new Promise(resolve => setTimeout(resolve, 2000));
    setIsGenerating(false);
    
    // In production, this would call the SambaNova API
    console.log('Generating code for:', { type: selectedType, language: selectedLanguage, prompt });
  };

  const renderGenerationInterface = () => (
    <div className="space-y-6">
      <Card className="p-6 glow-effect">
        <h3 className="text-lg font-semibold flex items-center gap-2 mb-4">
          <Wand2 className="w-5 h-5 text-primary" />
          AI Code Generation
        </h3>
        
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium mb-2 block">Code Type</label>
              <Select value={selectedType} onValueChange={setSelectedType}>
                <SelectTrigger>
                  <SelectValue placeholder="Select code type" />
                </SelectTrigger>
                <SelectContent>
                  {codeTypes.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      <div className="flex items-center gap-2">
                        <type.icon className="w-4 h-4" />
                        {type.label}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <label className="text-sm font-medium mb-2 block">Language</label>
              <Select value={selectedLanguage} onValueChange={setSelectedLanguage}>
                <SelectTrigger>
                  <SelectValue placeholder="Select language" />
                </SelectTrigger>
                <SelectContent>
                  {languages[selectedType as keyof typeof languages]?.map((lang) => (
                    <SelectItem key={lang} value={lang}>
                      {lang.toUpperCase()}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <div>
            <label className="text-sm font-medium mb-2 block">Describe what you want to generate</label>
            <Textarea
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              placeholder="E.g., Create a responsive login form with validation, dark mode support, and smooth animations..."
              className="min-h-[100px]"
            />
          </div>
          
          <Button 
            onClick={handleGenerate}
            disabled={!prompt.trim() || isGenerating}
            className="w-full"
          >
            {isGenerating ? (
              <>
                <Sparkles className="w-4 h-4 mr-2 animate-spin" />
                Generating Code...
              </>
            ) : (
              <>
                <Code2 className="w-4 h-4 mr-2" />
                Generate Code
              </>
            )}
          </Button>
        </div>
      </Card>
      
      {isGenerating && (
        <Card className="p-6 glow-effect">
          <div className="text-center">
            <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4 floating-animation">
              <Sparkles className="w-6 h-6 text-primary animate-pulse" />
            </div>
            <h4 className="font-medium mb-2">AI is crafting your code...</h4>
            <p className="text-sm text-muted-foreground">
              Analyzing requirements, selecting best practices, and generating optimized code
            </p>
          </div>
        </Card>
      )}
    </div>
  );

  const renderGenerationHistory = () => (
    <div className="space-y-4">
      {recentGenerations.map((generation, index) => (
        <Card key={generation.id} className="p-6 glow-effect fade-in" style={{animationDelay: `${index * 0.1}s`}}>
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <Badge variant="outline" className="capitalize">
                {generation.type}
              </Badge>
              <Badge variant="secondary">
                {generation.language.toUpperCase()}
              </Badge>
              <Badge 
                variant={generation.status === 'integrated' ? 'default' : 'outline'}
                className="capitalize"
              >
                {generation.status}
              </Badge>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">
                Quality: {generation.quality}/10
              </span>
              <div className="flex gap-1">
                <Button variant="ghost" size="sm">
                  <Eye className="w-4 h-4" />
                </Button>
                <Button variant="ghost" size="sm">
                  <Copy className="w-4 h-4" />
                </Button>
                <Button variant="ghost" size="sm">
                  <Download className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>
          
          <div className="mb-4">
            <h4 className="font-medium mb-2">Prompt:</h4>
            <p className="text-sm text-muted-foreground bg-muted/50 p-3 rounded">
              {generation.prompt}
            </p>
          </div>
          
          <div className="relative">
            <h4 className="font-medium mb-2">Generated Code:</h4>
            <div className="bg-black/90 rounded-lg p-4 overflow-x-auto">
              <pre className="text-sm text-green-400 font-mono">
                <code>{generation.generatedCode}</code>
              </pre>
            </div>
          </div>
        </Card>
      ))}
    </div>
  );

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">AI Code Generation</h2>
        <p className="text-muted-foreground">
          Generate functional code snippets, components, and assets using advanced AI models
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="generate">Generate Code</TabsTrigger>
          <TabsTrigger value="history">Generation History</TabsTrigger>
        </TabsList>
        
        <TabsContent value="generate" className="mt-6">
          {renderGenerationInterface()}
        </TabsContent>
        
        <TabsContent value="history" className="mt-6">
          {renderGenerationHistory()}
        </TabsContent>
      </Tabs>
    </div>
  );
}