import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ArrowRight, Zap, Cpu, Users, Code, Rocket, Sparkles } from "lucide-react";
import { useLocation } from "wouter";

export default function Home() {
  const [, setLocation] = useLocation();

  const features = [
    {
      icon: Zap,
      title: "AI-Powered Generation",
      description: "Describe your app idea and watch our AI agents build it"
    },
    {
      icon: Users,
      title: "Multi-Agent Collaboration",
      description: "Specialized agents work together like a real development team"
    },
    {
      icon: Code,
      title: "Full-Stack Applications",
      description: "Complete applications with frontend, backend, and database"
    },
    {
      icon: Rocket,
      title: "Instant Deployment",
      description: "Your app is ready to deploy immediately after generation"
    }
  ];

  const models = [
    "Llama-4-Maverick-17B-128E-Instruct",
    "DeepSeek R1", 
    "Meta-Llama-3.3-70B-Instruct",
    "DeepSeek-R1-Distill-Llama-70B",
    "DeepSeek-V3-0324",
    "Llama-4-Scout-17B-16E-Instruct"
  ];

  return (
    <div className="min-h-screen gradient-bg relative overflow-hidden">
      {/* Header */}
      <header className="border-b border-border/50 bg-background/80 backdrop-blur-sm">
        <div className="container mx-auto px-6 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gradient-to-br from-primary to-primary/80 rounded-lg flex items-center justify-center">
              <Cpu className="w-5 h-5 text-primary-foreground" />
            </div>
            <span className="text-xl font-bold">AgentFlow Builder</span>
          </div>
          <div className="flex items-center space-x-4">
            <Badge variant="secondary" className="bg-green-500/10 text-green-600 border-green-500/20">
              ● Live
            </Badge>
            <Button variant="outline" size="sm">
              Documentation
            </Button>
          </div>
        </div>
      </header>

      {/* Floating Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-10 w-32 h-32 bg-primary/5 rounded-full floating-animation"></div>
        <div className="absolute top-40 right-20 w-24 h-24 bg-primary/3 rounded-full floating-animation" style={{animationDelay: '2s'}}></div>
        <div className="absolute bottom-32 left-1/4 w-20 h-20 bg-primary/4 rounded-full floating-animation" style={{animationDelay: '4s'}}></div>
      </div>

      {/* Hero Section */}
      <section className="container mx-auto px-6 py-20 relative z-10">
        <div className="text-center max-w-4xl mx-auto">
          <div className="inline-flex items-center space-x-2 bg-primary/10 text-primary px-4 py-2 rounded-full text-sm font-medium mb-8 glow-effect bounce-in">
            <Zap className="w-4 h-4 animate-pulse" />
            <span>No-Code AI App Builder</span>
          </div>
          
          <h1 className="text-5xl md:text-6xl font-bold tracking-tight mb-6 slide-up">
            Build Full-Stack Apps with
            <span className="bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent"> AI Agents</span>
          </h1>
          
          <p className="text-xl text-muted-foreground mb-12 max-w-2xl mx-auto leading-relaxed fade-in" style={{animationDelay: '0.2s'}}>
            Describe your app idea in plain English and watch our specialized AI agents collaborate to build a complete, 
            deployable application. No coding required.
          </p>

          <div className="flex flex-col sm:flex-row items-center justify-center gap-4 mb-16 fade-in" style={{animationDelay: '0.4s'}}>
            <Button 
              size="lg" 
              className="h-12 px-8 text-base font-medium glow-effect hover:scale-105 transition-all duration-300"
              onClick={() => setLocation("/builder")}
            >
              Start Building
              <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
            </Button>
            <Button variant="outline" size="lg" className="h-12 px-8 text-base hover:scale-105 transition-all duration-300">
              View Examples
            </Button>
          </div>

          {/* AI Models Preview */}
          <div className="bg-card border border-border rounded-xl p-6 mb-16 fade-in glow-effect" style={{animationDelay: '0.6s'}}>
            <h3 className="text-lg font-semibold mb-4">Powered by Advanced AI Models</h3>
            <div className="flex flex-wrap justify-center gap-3">
              {models.map((model, index) => (
                <Badge 
                  key={index} 
                  variant="secondary" 
                  className="text-xs px-3 py-1 hover:scale-105 transition-transform duration-200 shimmer"
                  style={{animationDelay: `${index * 0.1}s`}}
                >
                  {model}
                </Badge>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Features */}
      <section className="container mx-auto px-6 py-16">
        <div className="text-center mb-16">
          <h2 className="text-3xl font-bold mb-4">How It Works</h2>
          <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
            Our AI agents work like a real development team to transform your ideas into reality
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
          {features.map((feature, index) => (
            <Card 
              key={index} 
              className="p-6 card-hover glow-effect fade-in" 
              style={{animationDelay: `${0.8 + index * 0.1}s`}}
            >
              <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4 floating-animation" style={{animationDelay: `${index * 0.5}s`}}>
                <feature.icon className="w-6 h-6 text-primary" />
              </div>
              <h3 className="font-semibold mb-2">{feature.title}</h3>
              <p className="text-sm text-muted-foreground">{feature.description}</p>
            </Card>
          ))}
        </div>
      </section>

      {/* CTA Section */}
      <section className="container mx-auto px-6 py-20 relative z-10">
        <Card className="relative overflow-hidden glow-effect card-hover fade-in" style={{animationDelay: '1.2s'}}>
          <div className="absolute inset-0 bg-gradient-to-r from-primary/10 to-primary/5 shimmer"></div>
          <div className="relative p-12 text-center">
            <h2 className="text-3xl font-bold mb-4">Ready to Build Your App?</h2>
            <p className="text-muted-foreground text-lg mb-8 max-w-2xl mx-auto">
              Join thousands of users who have created amazing applications without writing a single line of code.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button 
                size="lg" 
                className="h-12 px-8 text-base font-medium glow-effect hover:scale-105 transition-all duration-300 group"
                onClick={() => setLocation("/builder")}
              >
                Start Building
                <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
              </Button>
              <Button 
                size="lg" 
                variant="outline"
                className="h-12 px-8 text-base font-medium glow-effect hover:scale-105 transition-all duration-300 group"
                onClick={() => setLocation("/advanced")}
              >
                Advanced Features
                <Sparkles className="w-5 h-5 ml-2 group-hover:rotate-12 transition-transform" />
              </Button>
            </div>
          </div>
        </Card>
      </section>
    </div>
  );
}