import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { AgentCollaborationPanel } from '@/components/agent-collaboration-panel';
import { CodeGenerationPanel } from '@/components/code-generation-panel';
import { LivePreviewPanel } from '@/components/live-preview-panel';
import { 
  Brain, 
  Code2, 
  Eye, 
  TrendingUp, 
  Users, 
  Zap,
  Shield,
  Database,
  Globe,
  AlertTriangle,
  CheckCircle,
  Clock
} from 'lucide-react';

export default function AdvancedDashboard() {
  const [activeSection, setActiveSection] = useState('overview');

  // Mock data for demonstration
  const projectMetrics = {
    totalProjects: 24,
    activeAgents: 12,
    codeGenerated: 156,
    successRate: 94,
    avgBuildTime: 2.3,
    userSatisfaction: 4.8
  };

  const recentActivity = [
    {
      id: 1,
      type: 'collaboration',
      message: 'Frontend and Backend agents resolved API integration conflict',
      timestamp: '2 minutes ago',
      status: 'success'
    },
    {
      id: 2,
      type: 'generation',
      message: 'Generated responsive dashboard component with 98% quality score',
      timestamp: '5 minutes ago',
      status: 'success'
    },
    {
      id: 3,
      type: 'deployment',
      message: 'Auto-deployed E-commerce platform v1.2.3 successfully',
      timestamp: '12 minutes ago',
      status: 'success'
    },
    {
      id: 4,
      type: 'learning',
      message: 'AI discovered new optimization pattern for React components',
      timestamp: '18 minutes ago',
      status: 'info'
    },
    {
      id: 5,
      type: 'alert',
      message: 'Performance monitoring detected slow API response in project X',
      timestamp: '25 minutes ago',
      status: 'warning'
    }
  ];

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'collaboration': return Users;
      case 'generation': return Code2;
      case 'deployment': return Globe;
      case 'learning': return Brain;
      case 'alert': return AlertTriangle;
      default: return CheckCircle;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'text-green-500';
      case 'warning': return 'text-yellow-500';
      case 'error': return 'text-red-500';
      default: return 'text-blue-500';
    }
  };

  const renderOverview = () => (
    <div className="space-y-6">
      {/* Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
        <Card className="p-4 glow-effect text-center fade-in">
          <div className="text-2xl font-bold text-blue-600">{projectMetrics.totalProjects}</div>
          <div className="text-sm text-muted-foreground">Total Projects</div>
        </Card>
        <Card className="p-4 glow-effect text-center fade-in" style={{animationDelay: '0.1s'}}>
          <div className="text-2xl font-bold text-green-600">{projectMetrics.activeAgents}</div>
          <div className="text-sm text-muted-foreground">Active Agents</div>
        </Card>
        <Card className="p-4 glow-effect text-center fade-in" style={{animationDelay: '0.2s'}}>
          <div className="text-2xl font-bold text-purple-600">{projectMetrics.codeGenerated}K</div>
          <div className="text-sm text-muted-foreground">Lines Generated</div>
        </Card>
        <Card className="p-4 glow-effect text-center fade-in" style={{animationDelay: '0.3s'}}>
          <div className="text-2xl font-bold text-orange-600">{projectMetrics.successRate}%</div>
          <div className="text-sm text-muted-foreground">Success Rate</div>
        </Card>
        <Card className="p-4 glow-effect text-center fade-in" style={{animationDelay: '0.4s'}}>
          <div className="text-2xl font-bold text-cyan-600">{projectMetrics.avgBuildTime}s</div>
          <div className="text-sm text-muted-foreground">Avg Build Time</div>
        </Card>
        <Card className="p-4 glow-effect text-center fade-in" style={{animationDelay: '0.5s'}}>
          <div className="text-2xl font-bold text-pink-600">{projectMetrics.userSatisfaction}/5</div>
          <div className="text-sm text-muted-foreground">User Rating</div>
        </Card>
      </div>

      {/* Feature Showcase */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6 glow-effect fade-in" style={{animationDelay: '0.6s'}}>
          <h3 className="text-lg font-semibold flex items-center gap-2 mb-4">
            <Brain className="w-5 h-5 text-primary" />
            Autonomous Agent Evolution
          </h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-muted/50 rounded">
              <span className="text-sm">Self-Healing Detection</span>
              <Badge variant="default">Active</Badge>
            </div>
            <div className="flex items-center justify-between p-3 bg-muted/50 rounded">
              <span className="text-sm">Continuous Learning</span>
              <Badge variant="default">Enabled</Badge>
            </div>
            <div className="flex items-center justify-between p-3 bg-muted/50 rounded">
              <span className="text-sm">Auto Bug Fixes</span>
              <Badge variant="outline">12 Fixed Today</Badge>
            </div>
          </div>
        </Card>

        <Card className="p-6 glow-effect fade-in" style={{animationDelay: '0.7s'}}>
          <h3 className="text-lg font-semibold flex items-center gap-2 mb-4">
            <Shield className="w-5 h-5 text-primary" />
            Advanced Monitoring
          </h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-muted/50 rounded">
              <span className="text-sm">Performance Analytics</span>
              <Badge variant="default">Real-time</Badge>
            </div>
            <div className="flex items-center justify-between p-3 bg-muted/50 rounded">
              <span className="text-sm">Anomaly Detection</span>
              <Badge variant="default">AI-Powered</Badge>
            </div>
            <div className="flex items-center justify-between p-3 bg-muted/50 rounded">
              <span className="text-sm">Auto Scaling</span>
              <Badge variant="outline">3 Apps Scaled</Badge>
            </div>
          </div>
        </Card>
      </div>

      {/* Recent Activity */}
      <Card className="p-6 glow-effect fade-in" style={{animationDelay: '0.8s'}}>
        <h3 className="text-lg font-semibold flex items-center gap-2 mb-4">
          <TrendingUp className="w-5 h-5 text-primary" />
          Real-time Activity Feed
        </h3>
        <div className="space-y-3">
          {recentActivity.map((activity, index) => {
            const IconComponent = getActivityIcon(activity.type);
            return (
              <div key={activity.id} className="flex items-center gap-3 p-3 border rounded-lg fade-in" style={{animationDelay: `${0.9 + index * 0.1}s`}}>
                <div className={`w-8 h-8 rounded-full flex items-center justify-center bg-muted/50`}>
                  <IconComponent className={`w-4 h-4 ${getStatusColor(activity.status)}`} />
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium">{activity.message}</p>
                  <p className="text-xs text-muted-foreground flex items-center gap-1">
                    <Clock className="w-3 h-3" />
                    {activity.timestamp}
                  </p>
                </div>
                <Badge variant={activity.status === 'success' ? 'default' : activity.status === 'warning' ? 'destructive' : 'secondary'}>
                  {activity.status}
                </Badge>
              </div>
            );
          })}
        </div>
      </Card>
    </div>
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-primary/5">
      {/* Header */}
      <div className="border-b bg-background/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold">Advanced AI Development Hub</h1>
              <p className="text-muted-foreground">
                Sophisticated agent collaboration, code generation, and intelligent monitoring
              </p>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="default" className="animate-pulse">
                <Zap className="w-3 h-3 mr-1" />
                System Online
              </Badge>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-6 py-8">
        <Tabs value={activeSection} onValueChange={setActiveSection} className="w-full">
          <TabsList className="grid w-full grid-cols-4 mb-8">
            <TabsTrigger value="overview" className="flex items-center gap-2">
              <TrendingUp className="w-4 h-4" />
              Overview
            </TabsTrigger>
            <TabsTrigger value="collaboration" className="flex items-center gap-2">
              <Users className="w-4 h-4" />
              Agent Collaboration
            </TabsTrigger>
            <TabsTrigger value="generation" className="flex items-center gap-2">
              <Code2 className="w-4 h-4" />
              Code Generation
            </TabsTrigger>
            <TabsTrigger value="preview" className="flex items-center gap-2">
              <Eye className="w-4 h-4" />
              Live Preview
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="overview">
            {renderOverview()}
          </TabsContent>
          
          <TabsContent value="collaboration">
            <AgentCollaborationPanel 
              projectId={1} 
              agents={[
                { id: 1, projectId: 1, name: 'Team Leader', role: 'team_leader', status: 'active', config: {}, lastActivity: new Date() },
                { id: 2, projectId: 1, name: 'Frontend Developer', role: 'frontend_developer', status: 'active', config: {}, lastActivity: new Date() },
                { id: 3, projectId: 1, name: 'Backend Developer', role: 'backend_developer', status: 'active', config: {}, lastActivity: new Date() },
                { id: 4, projectId: 1, name: 'Performance Engineer', role: 'performance_engineer', status: 'active', config: {}, lastActivity: new Date() },
                { id: 5, projectId: 1, name: 'Data Analyst', role: 'data_analyst', status: 'active', config: {}, lastActivity: new Date() }
              ]} 
            />
          </TabsContent>
          
          <TabsContent value="generation">
            <CodeGenerationPanel projectId={1} />
          </TabsContent>
          
          <TabsContent value="preview">
            <LivePreviewPanel projectId={1} />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}