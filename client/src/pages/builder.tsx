import { useState } from "react";
import { useQ<PERSON>y, useMutation, useQueryClient } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";
import { 
  Send, Paperclip, Image, FileText, Zap, Users, Code, 
  Crown, ClipboardCheck, Compass, BarChart, Play, 
  ArrowLeft, Eye, Download, Settings 
} from "lucide-react";
import { TeamHeader } from "@/components/team-header";
import { ChatInterface } from "@/components/chat-interface";
import { StatusBar } from "@/components/status-bar";
import { useLocation } from "wouter";
import { useToast } from "@/hooks/use-toast";
import { useWebSocket } from "@/hooks/use-websocket";
import { apiRequest } from "@/lib/queryClient";
import type { Project, Agent } from "@shared/schema";

const AI_MODELS = [
  { value: "llama-4-maverick", label: "Llama-4-Maverick-17B-128E-Instruct", description: "Advanced reasoning and code generation" },
  { value: "deepseek-r1", label: "DeepSeek R1", description: "Latest reasoning model" },
  { value: "llama-3.3-70b", label: "Meta-Llama-3.3-70B-Instruct", description: "Large context understanding" },
  { value: "deepseek-r1-distill", label: "DeepSeek-R1-Distill-Llama-70B", description: "Efficient reasoning" },
  { value: "deepseek-v3", label: "DeepSeek-V3-0324", description: "Multi-modal capabilities" },
  { value: "llama-4-scout", label: "Llama-4-Scout-17B-16E-Instruct", description: "Fast generation" }
];

interface BuilderState {
  phase: 'input' | 'chat' | 'generation' | 'preview';
  selectedModel: string;
  requirement: string;
  generationProgress: number;
  activeAgents: Agent[];
}

export default function Builder() {
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  
  const [builderState, setBuilderState] = useState<BuilderState>({
    phase: 'input',
    selectedModel: 'llama-4-maverick',
    requirement: '',
    generationProgress: 0,
    activeAgents: []
  });

  const [selectedAgent, setSelectedAgent] = useState<Agent | null>(null);

  const { data: projects = [] } = useQuery<Project[]>({
    queryKey: ["/api/projects"],
  });

  const currentProject = projects[0] || null;

  const createProjectMutation = useMutation({
    mutationFn: async (requirement: string) => {
      const res = await apiRequest("POST", "/api/projects", {
        name: "AI Generated App",
        description: requirement,
        userId: 1
      });
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/projects"] });
      setBuilderState(prev => ({ ...prev, phase: 'chat' }));
      toast({
        title: "Project Created",
        description: "Starting agent collaboration...",
      });
    },
  });

  const runWorkflowMutation = useMutation({
    mutationFn: async () => {
      if (!currentProject) return;
      const res = await apiRequest("POST", `/api/projects/${currentProject.id}/run`);
      return res.json();
    },
    onSuccess: () => {
      if (currentProject) {
        queryClient.invalidateQueries({ queryKey: [`/api/projects/${currentProject.id}/agents`] });
        setBuilderState(prev => ({ ...prev, phase: 'generation', generationProgress: 0 }));
        
        // Simulate generation progress
        const interval = setInterval(() => {
          setBuilderState(prev => {
            const newProgress = prev.generationProgress + Math.random() * 15;
            if (newProgress >= 100) {
              clearInterval(interval);
              return { ...prev, generationProgress: 100, phase: 'preview' };
            }
            return { ...prev, generationProgress: newProgress };
          });
        }, 1000);
      }
    },
  });

  useWebSocket({
    onMessage: (message) => {
      switch (message.type) {
        case "agent_updated":
          toast({
            title: "Agent Active",
            description: `${message.agent.name} is now working on your app`,
          });
          break;
        case "workflow_started":
          toast({
            title: "Generation Started",
            description: "AI agents are building your application",
          });
          break;
      }
    },
  });

  const handleStartBuilding = () => {
    if (!builderState.requirement.trim()) {
      toast({
        title: "Requirement Needed",
        description: "Please describe your app idea first",
        variant: "destructive"
      });
      return;
    }
    
    createProjectMutation.mutate(builderState.requirement);
  };

  const handleGenerateApp = () => {
    runWorkflowMutation.mutate();
  };

  const renderInputPhase = () => (
    <div className="max-w-4xl mx-auto">
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold mb-4">Describe Your App Idea</h1>
        <p className="text-muted-foreground text-lg">
          Tell us what you want to build and our AI agents will create it for you
        </p>
      </div>

      <Card className="p-8 mb-8">
        <div className="space-y-6">
          <div>
            <label className="text-sm font-medium mb-3 block">AI Model Selection</label>
            <Select 
              value={builderState.selectedModel} 
              onValueChange={(value) => setBuilderState(prev => ({ ...prev, selectedModel: value }))}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Choose AI model" />
              </SelectTrigger>
              <SelectContent>
                {AI_MODELS.map((model) => (
                  <SelectItem key={model.value} value={model.value}>
                    <div className="flex flex-col">
                      <span className="font-medium">{model.label}</span>
                      <span className="text-xs text-muted-foreground">{model.description}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <label className="text-sm font-medium mb-3 block">App Requirements</label>
            <Textarea
              placeholder="Example: I want to build a social media app for pet owners where they can share photos of their pets, follow other pet owners, and find pet-friendly places nearby. It should have user authentication, photo upload, geolocation features, and a feed system..."
              value={builderState.requirement}
              onChange={(e) => setBuilderState(prev => ({ ...prev, requirement: e.target.value }))}
              className="min-h-[200px] text-base leading-relaxed"
            />
            <div className="flex items-center justify-between mt-3">
              <div className="flex items-center space-x-4">
                <Button variant="ghost" size="sm">
                  <Paperclip className="w-4 h-4 mr-2" />
                  Attach Files
                </Button>
                <Button variant="ghost" size="sm">
                  <Image className="w-4 h-4 mr-2" />
                  Add Mockups
                </Button>
              </div>
              <span className="text-xs text-muted-foreground">
                {builderState.requirement.length} characters
              </span>
            </div>
          </div>

          <Button 
            onClick={handleStartBuilding}
            className="w-full h-12 text-base font-medium"
            disabled={createProjectMutation.isPending || !builderState.requirement.trim()}
          >
            {createProjectMutation.isPending ? (
              "Starting..."
            ) : (
              <>
                <Zap className="w-5 h-5 mr-2" />
                Start Building with AI
              </>
            )}
          </Button>
        </div>
      </Card>

      <div className="grid md:grid-cols-3 gap-6">
        <Card className="p-6">
          <Users className="w-8 h-8 text-primary mb-4" />
          <h3 className="font-semibold mb-2">Team Leader Coordination</h3>
          <p className="text-sm text-muted-foreground">
            AI team leader assigns tasks and coordinates the development process
          </p>
        </Card>
        <Card className="p-6">
          <Code className="w-8 h-8 text-primary mb-4" />
          <h3 className="font-semibold mb-2">Full-Stack Generation</h3>
          <p className="text-sm text-muted-foreground">
            Complete applications with frontend, backend, and database
          </p>
        </Card>
        <Card className="p-6">
          <Eye className="w-8 h-8 text-primary mb-4" />
          <h3 className="font-semibold mb-2">Live Preview</h3>
          <p className="text-sm text-muted-foreground">
            See your app come to life in real-time as it's being built
          </p>
        </Card>
      </div>
    </div>
  );

  const renderGenerationPhase = () => (
    <div className="max-w-2xl mx-auto text-center">
      <div className="mb-8 fade-in">
        <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4 floating-animation glow-effect">
          <Zap className="w-8 h-8 text-primary animate-pulse" />
        </div>
        <h2 className="text-2xl font-bold mb-2 slide-up">AI Agents Building Your App</h2>
        <p className="text-muted-foreground fade-in" style={{animationDelay: '0.2s'}}>
          Our specialized agents are working together to create your application
        </p>
      </div>

      <Card className="p-6 mb-8 glow-effect fade-in" style={{animationDelay: '0.4s'}}>
        <div className="mb-4">
          <div className="flex justify-between text-sm mb-2">
            <span>Generation Progress</span>
            <span className="font-mono font-bold">{Math.round(builderState.generationProgress)}%</span>
          </div>
          <Progress value={builderState.generationProgress} className="h-3" />
        </div>
        <div className="text-sm text-muted-foreground shimmer">
          {builderState.generationProgress < 25 && "🔍 Analyzing requirements and planning architecture..."}
          {builderState.generationProgress >= 25 && builderState.generationProgress < 50 && "⚡ Generating backend APIs and database schema..."}
          {builderState.generationProgress >= 50 && builderState.generationProgress < 75 && "🎨 Building frontend components and user interface..."}
          {builderState.generationProgress >= 75 && builderState.generationProgress < 100 && "🔧 Integrating components and finalizing application..."}
          {builderState.generationProgress >= 100 && "🎉 Your application is ready!"}
        </div>
      </Card>

      <div className="grid grid-cols-2 gap-4">
        {[
          { icon: Crown, name: "Team Leader", status: "Coordinating", color: "emerald" },
          { icon: ClipboardCheck, name: "Product Manager", status: "Planning", color: "blue" },
          { icon: Compass, name: "Architect", status: "Designing", color: "violet" },
          { icon: BarChart, name: "Engineer", status: "Coding", color: "orange" }
        ].map((agent, index) => (
          <Card 
            key={index} 
            className="p-4 card-hover glow-effect bounce-in" 
            style={{animationDelay: `${0.6 + index * 0.1}s`}}
          >
            <div className="flex items-center space-x-3">
              <div className={`w-8 h-8 bg-${agent.color}-500/10 rounded-full flex items-center justify-center floating-animation`} style={{animationDelay: `${index * 0.5}s`}}>
                <agent.icon className={`w-4 h-4 text-${agent.color}-500`} />
              </div>
              <div className="text-left">
                <p className="font-medium text-sm">{agent.name}</p>
                <p className="text-xs text-muted-foreground typing-indicator">{agent.status}...</p>
              </div>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );

  const renderPreviewPhase = () => (
    <div className="max-w-6xl mx-auto">
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold mb-4">Your App is Ready! 🎉</h2>
        <p className="text-muted-foreground text-lg">
          Your AI-generated application is complete and ready for deployment
        </p>
      </div>

      <div className="grid lg:grid-cols-2 gap-8">
        <Card className="p-6">
          <h3 className="font-semibold mb-4">Live Preview</h3>
          <div className="bg-muted rounded-lg p-8 text-center min-h-[400px] flex items-center justify-center">
            <div>
              <Code className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">Your generated app would appear here</p>
              <p className="text-sm text-muted-foreground mt-2">
                Interactive preview with full functionality
              </p>
            </div>
          </div>
        </Card>

        <div className="space-y-6">
          <Card className="p-6">
            <h3 className="font-semibold mb-4">App Details</h3>
            <div className="space-y-3">
              <div>
                <span className="text-sm font-medium">Model Used:</span>
                <p className="text-sm text-muted-foreground">
                  {AI_MODELS.find(m => m.value === builderState.selectedModel)?.label}
                </p>
              </div>
              <div>
                <span className="text-sm font-medium">Technologies:</span>
                <div className="flex flex-wrap gap-2 mt-1">
                  {["React", "Node.js", "PostgreSQL", "Express", "TypeScript"].map(tech => (
                    <Badge key={tech} variant="secondary" className="text-xs">{tech}</Badge>
                  ))}
                </div>
              </div>
            </div>
          </Card>

          <Card className="p-6">
            <h3 className="font-semibold mb-4">Next Steps</h3>
            <div className="space-y-3">
              <Button className="w-full">
                <Download className="w-4 h-4 mr-2" />
                Download Source Code
              </Button>
              <Button variant="outline" className="w-full">
                <Eye className="w-4 h-4 mr-2" />
                Open in New Tab
              </Button>
              <Button variant="outline" className="w-full">
                <Settings className="w-4 h-4 mr-2" />
                Customize Further
              </Button>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-6 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="sm" onClick={() => setLocation("/")}>
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Home
            </Button>
            <Separator orientation="vertical" className="h-6" />
            <div className="flex items-center space-x-2">
              <Badge variant="secondary" className="capitalize">
                {builderState.phase}
              </Badge>
              <span className="text-sm text-muted-foreground">
                {builderState.phase === 'input' && "Describe your app"}
                {builderState.phase === 'chat' && "Refine requirements"}
                {builderState.phase === 'generation' && "AI is building"}
                {builderState.phase === 'preview' && "App ready"}
              </span>
            </div>
          </div>
          {builderState.phase === 'chat' && currentProject && (
            <Button onClick={handleGenerateApp} disabled={runWorkflowMutation.isPending}>
              <Play className="w-4 h-4 mr-2" />
              {runWorkflowMutation.isPending ? "Starting..." : "Generate App"}
            </Button>
          )}
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-6 py-12">
        {builderState.phase === 'input' && renderInputPhase()}
        {builderState.phase === 'generation' && renderGenerationPhase()}
        {builderState.phase === 'preview' && renderPreviewPhase()}
        
        {builderState.phase === 'chat' && currentProject && (
          <div className="max-w-6xl mx-auto">
            <div className="mb-6">
              <TeamHeader
                projectId={currentProject.id}
                selectedAgent={selectedAgent}
                onSelectAgent={setSelectedAgent}
                onRunWorkflow={handleGenerateApp}
              />
            </div>
            <div className="h-[600px] border border-border rounded-lg overflow-hidden">
              <ChatInterface
                projectId={currentProject.id}
                selectedAgent={selectedAgent}
              />
            </div>
          </div>
        )}
      </main>
    </div>
  );
}