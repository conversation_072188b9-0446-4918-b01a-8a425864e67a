import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { TeamHeader } from "@/components/team-header";
import { ChatInterface } from "@/components/chat-interface";
import { StatusBar } from "@/components/status-bar";
import { useWebSocket } from "@/hooks/use-websocket";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import type { Project, Agent } from "@shared/schema";

export default function Dashboard() {
  const [selectedAgent, setSelectedAgent] = useState<Agent | null>(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: projects = [] } = useQuery<Project[]>({
    queryKey: ["/api/projects"],
  });

  const currentProject = projects[0] || null;

  const runWorkflowMutation = useMutation({
    mutationFn: async () => {
      if (!currentProject) return;
      const res = await apiRequest("POST", `/api/projects/${currentProject.id}/run`);
      return res.json();
    },
    onSuccess: () => {
      if (currentProject) {
        queryClient.invalidateQueries({ queryKey: [`/api/projects/${currentProject.id}/agents`] });
        toast({
          title: "Workflow Started",
          description: "All agents are now active and processing tasks",
        });
      }
    },
  });

  useWebSocket({
    onMessage: (message) => {
      switch (message.type) {
        case "agent_updated":
          toast({
            title: "Agent Updated",
            description: `${message.agent.name} status changed to ${message.agent.status}`,
          });
          break;
        case "workflow_started":
          toast({
            title: "Workflow Started",
            description: "All agents are now active and processing tasks",
          });
          break;
        case "message_created":
          // Real-time message updates are handled by query invalidation
          break;
        default:
          break;
      }
    },
  });

  if (!currentProject) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <h2 className="text-2xl font-semibold mb-2">Loading Project...</h2>
          <p className="text-muted-foreground">Setting up your multi-agent workspace</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col bg-background">
      {/* Team Header - matches mgx.dev top bar */}
      <TeamHeader
        projectId={currentProject.id}
        selectedAgent={selectedAgent}
        onSelectAgent={setSelectedAgent}
        onRunWorkflow={() => runWorkflowMutation.mutate()}
      />
      
      {/* Main Chat Interface */}
      <div className="flex-1 flex flex-col overflow-hidden">
        <ChatInterface
          projectId={currentProject.id}
          selectedAgent={selectedAgent}
        />
      </div>

      {/* Status Bar - matches mgx.dev bottom controls */}
      <StatusBar isLive={true} />
    </div>
  );
}
