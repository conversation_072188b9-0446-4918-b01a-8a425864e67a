@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: 218 23% 97%;
  --foreground: 222 15% 15%;
  --muted: 220 14% 96%;
  --muted-foreground: 220 8% 46%;
  --popover: 0 0% 100%;
  --popover-foreground: 222 15% 15%;
  --card: 0 0% 100%;
  --card-foreground: 222 15% 15%;
  --border: 220 13% 91%;
  --input: 220 13% 91%;
  --primary: 221 83% 53%;
  --primary-foreground: 210 40% 98%;
  --secondary: 220 14% 96%;
  --secondary-foreground: 220 9% 9%;
  --accent: 220 14% 96%;
  --accent-foreground: 220 9% 9%;
  --destructive: 0 84% 60%;
  --destructive-foreground: 210 40% 98%;
  --ring: 221 83% 53%;
  --radius: 0.75rem;
}

.dark {
  --background: 217 19% 8%;
  --foreground: 220 14% 96%;
  --muted: 215 20% 12%;
  --muted-foreground: 217 10% 64%;
  --popover: 217 19% 8%;
  --popover-foreground: 220 14% 96%;
  --card: 215 20% 10%;
  --card-foreground: 220 14% 96%;
  --border: 215 20% 16%;
  --input: 215 20% 16%;
  --primary: 221 83% 53%;
  --primary-foreground: 210 40% 98%;
  --secondary: 215 20% 12%;
  --secondary-foreground: 220 14% 96%;
  --accent: 215 20% 12%;
  --accent-foreground: 220 14% 96%;
  --destructive: 0 76% 60%;
  --destructive-foreground: 210 40% 98%;
  --ring: 221 83% 53%;
}

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground font-sans antialiased;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  
  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold;
  }
}

@layer components {
  .gradient-bg {
    background: linear-gradient(135deg, hsl(var(--background)) 0%, hsl(var(--muted)) 100%);
  }
  
  .floating-animation {
    animation: float 6s ease-in-out infinite;
  }
  
  .glow-effect {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.15);
    transition: box-shadow 0.3s ease;
  }
  
  .glow-effect:hover {
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.25);
  }
  
  .shimmer {
    background: linear-gradient(90deg, transparent 30%, rgba(255,255,255,0.2) 50%, transparent 70%);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
  }
  
  .fade-in {
    animation: fadeIn 0.6s ease-out forwards;
  }
  
  .slide-up {
    animation: slideUp 0.5s ease-out forwards;
  }
  
  .bounce-in {
    animation: bounceIn 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55) forwards;
  }
  
  .agent-avatar {
    transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  }
  
  .agent-avatar:hover {
    transform: scale(1.1) translateY(-2px);
  }
  
  .typing-indicator {
    animation: typing 1.4s infinite;
  }
  
  .progress-bar {
    background: linear-gradient(90deg, hsl(var(--primary)) 0%, hsl(var(--primary)/0.8) 100%);
    animation: progressGlow 2s ease-in-out infinite alternate;
  }
  
  .card-hover {
    transition: all 0.3s ease;
  }
  
  .card-hover:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
  }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideUp {
  from { opacity: 0; transform: translateY(30px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes bounceIn {
  0% { opacity: 0; transform: scale(0.3); }
  50% { opacity: 1; transform: scale(1.05); }
  70% { transform: scale(0.9); }
  100% { opacity: 1; transform: scale(1); }
}

@keyframes typing {
  0%, 60%, 100% { transform: translateY(0); }
  30% { transform: translateY(-10px); }
}

@keyframes progressGlow {
  0% { box-shadow: 0 0 5px hsl(var(--primary)/0.5); }
  100% { box-shadow: 0 0 20px hsl(var(--primary)/0.8), 0 0 30px hsl(var(--primary)/0.4); }
}

@keyframes pulse-slow {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% { transform: translate3d(0,0,0); }
  40%, 43% { transform: translate3d(0, -8px, 0); }
  70% { transform: translate3d(0, -4px, 0); }
  90% { transform: translate3d(0, -2px, 0); }
}

@layer utilities {
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  
  .animate-pulse-slow {
    animation: pulse-slow 3s ease-in-out infinite;
  }
  
  .animate-rotate {
    animation: rotate 2s linear infinite;
  }
  
  .animate-bounce-subtle {
    animation: bounce 2s infinite;
  }
}
